import React from 'react';
import { ColeccionFlashcards } from '@/lib/supabase';
import { FiInbox, FiTrash2, FiRefreshCw, FiEdit2 } from 'react-icons/fi';

interface FlashcardCollectionListProps {
  colecciones: ColeccionFlashcards[];
  coleccionSeleccionada: ColeccionFlashcards | null;
  onSeleccionarColeccion: (coleccion: ColeccionFlashcards) => void;
  onEliminarColeccion: (coleccionId: string) => void;
  onEditarColeccion: (coleccion: ColeccionFlashcards) => void;
  isLoading: boolean;
  deletingId?: string | null;
}

const FlashcardCollectionList: React.FC<FlashcardCollectionListProps> = ({
  colecciones,
  coleccionSeleccionada,
  onSeleccionarColeccion,
  onEliminarColeccion,
  onEditarColeccion,
  isLoading,
  deletingId
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (colecciones.length === 0) {
    return (
      <div className="text-center p-8 border-2 border-dashed border-gray-300 rounded-lg">
        <FiInbox className="mx-auto text-6xl text-gray-400 mb-4" />
        <p className="text-gray-500 text-lg">No hay colecciones de flashcards disponibles.</p>
        <p className="text-sm text-gray-400 mt-1">Crea una nueva colección para empezar a estudiar.</p>
      </div>
    );
  }

  const handleEliminarClick = (e: React.MouseEvent, coleccionId: string) => {
    e.stopPropagation(); // Evitar que se seleccione la colección al hacer clic en eliminar
    onEliminarColeccion(coleccionId);
  };

  const handleEditarClick = (e: React.MouseEvent, coleccion: ColeccionFlashcards) => {
    e.stopPropagation(); // Evitar que se seleccione la colección al hacer clic en editar
    onEditarColeccion(coleccion);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
      {colecciones.map((coleccion) => (
        <div
          key={coleccion.id}
          className="border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors flex flex-col justify-between"
          onClick={() => onSeleccionarColeccion(coleccion)}
        >
          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-lg">{coleccion.titulo}</h3>
              <button
                onClick={(e) => handleEditarClick(e, coleccion)}
                className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                title="Editar colección"
              >
                <FiEdit2 size={16} />
              </button>
            </div>
            {coleccion.descripcion && (
              <p className="text-sm text-gray-600 mb-2 break-words">{coleccion.descripcion}</p>
            )}
            <p className="text-sm text-gray-500 mb-1">
              Flashcards: {typeof coleccion.numero_flashcards === 'number' ? coleccion.numero_flashcards : 'N/A'}
            </p>
            <p className="text-xs text-gray-400">
              Creada: {new Date(coleccion.creado_en).toLocaleDateString('es-ES')}
            </p>
          </div>
          <div className="mt-4 space-y-2">
            <div className="flex gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onSeleccionarColeccion(coleccion);
                }}
                className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex-1"
              >
                Estudiar
              </button>
            </div>
            <button
              onClick={(e) => handleEliminarClick(e, coleccion.id)}
              disabled={deletingId === coleccion.id}
              className="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center justify-center disabled:opacity-50"
            >
              {deletingId === coleccion.id ? (
                <>
                  <FiRefreshCw size={16} className="animate-spin mr-2" />
                  Eliminando...
                </>
              ) : (
                <>
                  <FiTrash2 size={16} className="mr-2" />
                  Eliminar
                </>
              )}
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FlashcardCollectionList;
