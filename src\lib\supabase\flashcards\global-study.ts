/**
 * Funciones globales para estudio multi-colección
 * 
 * Responsabilidades:
 * - Obtención de flashcards de todas las colecciones del usuario
 * - Modos de estudio globales (difíciles, aleatorias, no recientes, por estado)
 * - Agregación de datos de múltiples colecciones
 */

import { FlashcardConProgreso } from '../supabaseClient';
import { obtenerColeccionesFlashcards } from './collections';
import {
  obtenerFlashcardsMasDificiles,
  obtenerFlashcardsParaEstudiar,
  obtenerFlashcardsNoRecientes,
  obtenerFlashcardsPorEstado
} from './study-modes';

/**
 * Obtiene flashcards más difíciles de todas las colecciones del usuario
 */
export async function obtenerFlashcardsMasDificilesGlobal(limite: number = 20): Promise<FlashcardConProgreso[]> {
  try {
    // Obtener todas las colecciones del usuario
    const colecciones = await obtenerColeccionesFlashcards();

    if (colecciones.length === 0) {
      return [];
    }

    // Obtener flashcards difíciles de cada colección
    const flashcardsPorColeccion = await Promise.all(
      colecciones.map(async (coleccion) => {
        return await obtenerFlashcardsMasDificiles(coleccion.id, limite);
      })
    );

    // Combinar todas las flashcards
    const todasLasFlashcards = flashcardsPorColeccion.flat();

    // Mezclar y limitar
    const flashcardsMezcladas = [...todasLasFlashcards]
      .sort(() => Math.random() - 0.5)
      .slice(0, limite);

    return flashcardsMezcladas;
  } catch (error) {
    console.error('Error al obtener flashcards difíciles globales:', error);
    return [];
  }
}

/**
 * Obtiene flashcards aleatorias de todas las colecciones del usuario
 */
export async function obtenerFlashcardsAleatoriasGlobal(limite: number = 20): Promise<FlashcardConProgreso[]> {
  try {
    // Obtener todas las colecciones del usuario
    const colecciones = await obtenerColeccionesFlashcards();

    if (colecciones.length === 0) {
      return [];
    }

    // Obtener todas las flashcards de todas las colecciones
    const flashcardsPorColeccion = await Promise.all(
      colecciones.map(async (coleccion) => {
        return await obtenerFlashcardsParaEstudiar(coleccion.id);
      })
    );

    // Combinar todas las flashcards
    const todasLasFlashcards = flashcardsPorColeccion.flat();

    // Mezclar aleatoriamente y tomar el límite
    const flashcardsMezcladas = [...todasLasFlashcards]
      .sort(() => Math.random() - 0.5)
      .slice(0, limite);

    return flashcardsMezcladas;
  } catch (error) {
    console.error('Error al obtener flashcards aleatorias globales:', error);
    return [];
  }
}

/**
 * Obtiene flashcards no estudiadas recientemente de todas las colecciones del usuario
 */
export async function obtenerFlashcardsNoRecientesGlobal(limite: number = 20): Promise<FlashcardConProgreso[]> {
  try {
    // Obtener todas las colecciones del usuario
    const colecciones = await obtenerColeccionesFlashcards();

    if (colecciones.length === 0) {
      return [];
    }

    // Obtener flashcards no recientes de cada colección
    const flashcardsPorColeccion = await Promise.all(
      colecciones.map(async (coleccion) => {
        return await obtenerFlashcardsNoRecientes(coleccion.id, limite);
      })
    );

    // Combinar todas las flashcards
    const todasLasFlashcards = flashcardsPorColeccion.flat();

    // Ordenar por fecha de próxima revisión (más antiguas primero) y limitar
    const flashcardsOrdenadas = todasLasFlashcards
      .sort((a, b) => {
        const fechaA = a.progreso?.proxima_revision ? new Date(a.progreso.proxima_revision).getTime() : 0;
        const fechaB = b.progreso?.proxima_revision ? new Date(b.progreso.proxima_revision).getTime() : 0;
        return fechaA - fechaB;
      })
      .slice(0, limite);

    return flashcardsOrdenadas;
  } catch (error) {
    console.error('Error al obtener flashcards no recientes globales:', error);
    return [];
  }
}

/**
 * Obtiene flashcards por estado específico de todas las colecciones del usuario
 */
export async function obtenerFlashcardsPorEstadoGlobal(
  estado: 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido',
  limite: number = 20
): Promise<FlashcardConProgreso[]> {
  try {
    // Obtener todas las colecciones del usuario
    const colecciones = await obtenerColeccionesFlashcards();

    if (colecciones.length === 0) {
      return [];
    }

    // Obtener flashcards por estado de cada colección
    const flashcardsPorColeccion = await Promise.all(
      colecciones.map(async (coleccion) => {
        return await obtenerFlashcardsPorEstado(coleccion.id, estado, limite);
      })
    );

    // Combinar todas las flashcards
    const todasLasFlashcards = flashcardsPorColeccion.flat();

    // Mezclar y limitar
    const flashcardsMezcladas = [...todasLasFlashcards]
      .sort(() => Math.random() - 0.5)
      .slice(0, limite);

    return flashcardsMezcladas;
  } catch (error) {
    console.error(`Error al obtener flashcards ${estado} globales:`, error);
    return [];
  }
}
