// src/hooks/useUserPlan.ts
// Hook para obtener el plan del usuario actual

import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { userPlanCache } from '@/lib/cache/userPlanCache';

interface UserPlanData {
  plan: string;
  isLoading: boolean;
  error: string | null;
}

export function useUserPlan(): UserPlanData {
  const [plan, setPlan] = useState<string>('free');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const fetchUserPlan = async () => {
      if (!user?.id) {
        setPlan('free');
        setIsLoading(false);
        return;
      }

      // Verificar cache primero usando el sistema centralizado
      const cachedPlan = userPlanCache.getPlan(user.id);
      if (cachedPlan) {
        setPlan(cachedPlan);
        setIsLoading(false);
        return;
      }

      // Limpiar debounce anterior si existe
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      // Implementar debouncing de 500ms
      debounceRef.current = setTimeout(async () => {
        try {
          setIsLoading(true);
          setError(null);

          const response = await fetch('/api/user/plan');

          if (!response.ok) {
            throw new Error('Error obteniendo plan del usuario');
          }

          const data = await response.json();
          const userPlan = data.plan || 'free';

          setPlan(userPlan);
          userPlanCache.setPlan(user.id, userPlan);

        } catch (err) {
          console.error('Error fetching user plan:', err);
          setError(err instanceof Error ? err.message : 'Error desconocido');
          setPlan('free'); // Fallback a plan gratuito
        } finally {
          setIsLoading(false);
        }
      }, 500);
    };

    fetchUserPlan();

    // Cleanup function
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [user?.id]); // Cambio crítico: usar user?.id en lugar de user

  return {
    plan,
    isLoading,
    error
  };
}
