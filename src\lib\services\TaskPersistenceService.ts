'use client';

import { BackgroundTask } from '@/contexts/BackgroundTasksContext';

const STORAGE_KEY = 'background-tasks';
const MAX_TASK_AGE_HOURS = 24;

export interface SerializedBackgroundTask extends Omit<BackgroundTask, 'createdAt' | 'completedAt'> {
  createdAt: string;
  completedAt?: string;
}

/**
 * Servicio para manejar la persistencia de tareas en segundo plano
 * Siguiendo el patrón Service Layer de la arquitectura
 */
export class TaskPersistenceService {
  private static instance: TaskPersistenceService;

  private constructor() {}

  static getInstance(): TaskPersistenceService {
    if (!TaskPersistenceService.instance) {
      TaskPersistenceService.instance = new TaskPersistenceService();
    }
    return TaskPersistenceService.instance;
  }

  /**
   * Verifica si localStorage está disponible
   */
  private isStorageAvailable(): boolean {
    try {
      // Verificar si localStorage existe y es funcional
      if (typeof localStorage === 'undefined') {
        return false;
      }

      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Serializa una tarea para almacenamiento
   */
  private serializeTask(task: BackgroundTask): SerializedBackgroundTask {
    return {
      ...task,
      createdAt: task.createdAt.toISOString(),
      completedAt: task.completedAt?.toISOString()
    };
  }

  /**
   * Deserializa una tarea desde almacenamiento
   */
  private deserializeTask(serializedTask: SerializedBackgroundTask): BackgroundTask {
    return {
      ...serializedTask,
      createdAt: new Date(serializedTask.createdAt),
      completedAt: serializedTask.completedAt ? new Date(serializedTask.completedAt) : undefined
    };
  }

  /**
   * Verifica si una tarea es demasiado antigua
   */
  private isTaskExpired(task: BackgroundTask): boolean {
    const now = new Date();
    const taskAge = now.getTime() - task.createdAt.getTime();
    const maxAge = MAX_TASK_AGE_HOURS * 60 * 60 * 1000; // Convertir a milisegundos
    return taskAge > maxAge;
  }

  /**
   * Limpia tareas expiradas de una lista
   */
  private cleanExpiredTasks(tasks: BackgroundTask[]): BackgroundTask[] {
    return tasks.filter(task => !this.isTaskExpired(task));
  }

  /**
   * Guarda las tareas en localStorage
   */
  saveTasks(tasks: BackgroundTask[]): void {
    if (!this.isStorageAvailable()) {
      console.warn('localStorage no está disponible, no se pueden persistir las tareas');
      return;
    }

    try {
      // Limpiar tareas expiradas antes de guardar
      const cleanTasks = this.cleanExpiredTasks(tasks);

      // Serializar tareas
      const serializedTasks = cleanTasks.map(task => this.serializeTask(task));

      // Guardar en localStorage
      localStorage.setItem(STORAGE_KEY, JSON.stringify(serializedTasks));

      console.log(`💾 Guardadas ${cleanTasks.length} tareas en localStorage`);
    } catch (error) {
      console.error('Error al guardar tareas en localStorage:', error);
    }
  }

  /**
   * Carga las tareas desde localStorage
   */
  loadTasks(): BackgroundTask[] {
    if (!this.isStorageAvailable()) {
      return [];
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) {
        return [];
      }

      const serializedTasks: SerializedBackgroundTask[] = JSON.parse(stored);
      
      // Deserializar y limpiar tareas expiradas
      const tasks = serializedTasks.map(task => this.deserializeTask(task));
      const cleanTasks = this.cleanExpiredTasks(tasks);

      // Si se limpiaron tareas, actualizar el almacenamiento
      if (cleanTasks.length !== tasks.length) {
        this.saveTasks(cleanTasks);
      }

      console.log(`📂 Cargadas ${cleanTasks.length} tareas desde localStorage`);
      return cleanTasks;
    } catch (error) {
      console.error('Error al cargar tareas desde localStorage:', error);
      // En caso de error, limpiar el almacenamiento corrupto
      this.clearTasks();
      return [];
    }
  }

  /**
   * Limpia todas las tareas del almacenamiento
   */
  clearTasks(): void {
    if (!this.isStorageAvailable()) {
      return;
    }

    try {
      localStorage.removeItem(STORAGE_KEY);
      console.log('🗑️ Tareas limpiadas del localStorage');
    } catch (error) {
      console.error('Error al limpiar tareas del localStorage:', error);
    }
  }

  /**
   * Obtiene estadísticas de las tareas almacenadas
   */
  getTaskStats(): {
    total: number;
    byStatus: Record<BackgroundTask['status'], number>;
    byType: Record<BackgroundTask['type'], number>;
  } {
    const tasks = this.loadTasks();
    
    const stats = {
      total: tasks.length,
      byStatus: {
        pending: 0,
        processing: 0,
        completed: 0,
        error: 0
      } as Record<BackgroundTask['status'], number>,
      byType: {
        'mapa-mental': 0,
        'test': 0,
        'flashcards': 0,
        'plan-estudios': 0,
        'resumen': 0
      } as Record<BackgroundTask['type'], number>
    };

    tasks.forEach(task => {
      stats.byStatus[task.status]++;
      stats.byType[task.type]++;
    });

    return stats;
  }
}

// Exportar instancia singleton
export const taskPersistenceService = TaskPersistenceService.getInstance();
