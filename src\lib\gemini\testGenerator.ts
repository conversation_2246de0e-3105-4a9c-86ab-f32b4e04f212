import { prepararDocumentos } from './geminiClient';
import { PROMPT_TESTS } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { TestResultCombiner } from '@/lib/services/ResultCombinerService';
import { type Chunk } from '@/lib/utils/textProcessing';
import { seleccionarChunksRelevantes } from '@/lib/utils/chunkSelector';

/**
 * Genera un test con preguntas de opción múltiple a partir de los documentos
 */
export async function generarTest(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  try {
    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);

    // Verificar si se usó chunking
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      console.log(`🧪 Procesando test con chunking: ${resultadoDocumentos.content.length} chunks`);
      return await procesarTestConChunks(resultadoDocumentos.content, cantidad, instrucciones, documentos);
    } else {
      // Procesamiento tradicional para documentos sin chunking
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos) {
        throw new Error("No se han proporcionado documentos para generar el test.");
      }

      console.log(`🧪 Procesando test sin chunking`);
      return await procesarTestSinChunks(contenidoDocumentos, cantidad, instrucciones);
    }


  } catch (error) {
    console.error('Error al generar test:', error);
    throw error;
  }
}

/**
 * Procesa test con chunking - selecciona chunks relevantes y los procesa individualmente
 */
async function procesarTestConChunks(
  chunks: Chunk[],
  cantidad: number,
  instrucciones?: string,
  documentos?: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  const resultadosChunks: any[][] = [];
  const chunkingContexts: any[] = [];

  // --- INICIO DE LA NUEVA LÓGICA DE SELECCIÓN ---
  console.log(`🧪 Seleccionando chunks relevantes para la petición: "${instrucciones}"`);
  const selectionResult = seleccionarChunksRelevantes(chunks, instrucciones || '');

  if (selectionResult.selectedChunks.length === 0) {
    console.warn("No se encontraron chunks relevantes. Procesando todos como fallback.");
    // Como fallback, podríamos procesar todos, pero es mejor notificar al usuario.
    throw new Error("No se pudo encontrar contenido relacionado con tu petición. Intenta ser más específico.");
  }

  console.log(`🧪 Chunks relevantes encontrados: ${selectionResult.selectedChunks.length} de ${chunks.length}`);
  // --- FIN DE LA NUEVA LÓGICA DE SELECCIÓN ---

  // MODIFICACIÓN: Iterar sobre los chunks relevantes en lugar de todos
  for (let i = 0; i < selectionResult.selectedChunks.length; i++) {
    const chunkObj = selectionResult.selectedChunks[i];
    const chunkContent = chunkObj.content; // Usar el contenido del objeto Chunk
    console.log(`🧪 Procesando chunk relevante ${i + 1}/${selectionResult.selectedChunks.length}`);

    // MODIFICACIÓN: Calcular cantidad proporcional sobre los chunks relevantes
    const cantidadPorChunk = Math.ceil(cantidad / selectionResult.selectedChunks.length);

    // Construir prompt para este chunk específico
    let promptChunk = PROMPT_TESTS
      .replace('{documentos}', chunkContent) // Usar chunkContent
      .replace('{cantidad}', cantidadPorChunk.toString());

    // Añadir instrucciones adicionales si se proporcionan
    if (instrucciones) {
      promptChunk = promptChunk.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
    } else {
      promptChunk = promptChunk.replace('{instrucciones}', '');
    }

    // Obtener configuración específica para tests
    const config = getOpenAIConfig('TESTS');

    // Generar preguntas para este chunk
    const messages = [{ role: 'user' as const, content: promptChunk }];
    const responseText = await llamarOpenAI(messages, {
      ...config,
      activityName: `Generación de Test - Chunk ${i + 1}/${selectionResult.selectedChunks.length} (${cantidadPorChunk} preguntas)`
    });

    // Extraer el JSON de la respuesta
    const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

    if (!jsonMatch) {
      console.warn(`No se pudo extraer JSON del chunk ${i + 1}, continuando con el siguiente`);
      continue;
    }

    try {
      const testJson = jsonMatch[0];

      // Corregir errores comunes de formato antes del parsing
      const testJsonCorregido = testJson
        .replace(/"opcion([abcd])"/g, '"opcion_$1"')
        .replace(/"opciona"/g, '"opcion_a"')
        .replace(/"opcionb"/g, '"opcion_b"')
        .replace(/"opcionc"/g, '"opcion_c"')
        .replace(/"opciond"/g, '"opcion_d"');

      const preguntasChunk = JSON.parse(testJsonCorregido);

      // Validar formato básico
      if (Array.isArray(preguntasChunk) && preguntasChunk.length > 0) {
        resultadosChunks.push(preguntasChunk);

        // Guardar contexto del chunk para el combinador
        chunkingContexts.push({
          chunkIndex: i, // Usamos el índice del bucle de chunks relevantes
          chunkSize: chunkContent.length,
          questionsGenerated: preguntasChunk.length
        });
      }
    } catch (parseError) {
      console.warn(`Error parseando JSON del chunk ${i + 1}:`, parseError);
      continue;
    }
  }

  // Combinar resultados usando ResultCombinerService
  if (resultadosChunks.length > 0) {
    // Convertir formato de preguntas para el combinador
    const resultadosParaCombinar = resultadosChunks.map(chunk =>
      chunk.map((pregunta: any) => ({
        pregunta: pregunta.pregunta,
        opciones: [pregunta.opcion_a, pregunta.opcion_b, pregunta.opcion_c, pregunta.opcion_d],
        respuesta_correcta: pregunta.respuesta_correcta,
        explicacion: pregunta.explicacion
      }))
    );

    const combinedResult = TestResultCombiner.combine(
      resultadosParaCombinar,
      cantidad,
      chunkingContexts,
      {
        id: documentos?.[0]?.titulo || 'documento',
        title: documentos?.[0]?.titulo || 'Documento sin título',
        totalSize: chunks.reduce((total, chunk) => total + chunk.content.length, 0)
      }
    );

    // Convertir de vuelta al formato esperado
    const preguntasFinales = combinedResult.combinedResult.map(pregunta => ({
      pregunta: pregunta.pregunta,
      opcion_a: pregunta.opciones[0],
      opcion_b: pregunta.opciones[1],
      opcion_c: pregunta.opciones[2],
      opcion_d: pregunta.opciones[3],
      respuesta_correcta: pregunta.respuesta_correcta as 'a' | 'b' | 'c' | 'd'
    }));

    console.log(`🧪 Test generado con chunking: ${preguntasFinales.length} preguntas finales`);
    return preguntasFinales;
  } else {
    throw new Error("No se pudieron generar preguntas de ningún chunk");
  }
}

/**
 * Procesa test sin chunking - método tradicional
 */
async function procesarTestSinChunks(
  contenidoDocumentos: string,
  cantidad: number,
  instrucciones?: string
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  // Construir el prompt para la IA usando el prompt personalizado
  let prompt = PROMPT_TESTS
    .replace('{documentos}', contenidoDocumentos)
    .replace('{cantidad}', cantidad.toString());

  // Añadir instrucciones adicionales si se proporcionan
  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // Obtener configuración específica para tests
  const config = getOpenAIConfig('TESTS');

  console.log(`🧪 Generando test con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

  // Generar el test usando OpenAI con la configuración correcta
  const messages = [{ role: 'user' as const, content: prompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: `Generación de Test (${cantidad || 'N/A'} preguntas)`
  });

  // Extraer el JSON de la respuesta
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

  if (!jsonMatch) {
    console.log('❌ No se encontró JSON en la respuesta. Respuesta recibida:', responseText.substring(0, 500));
    throw new Error("No se pudo extraer el formato JSON de la respuesta.");
  }

  const testJson = jsonMatch[0];

  // Corregir errores comunes de formato antes del parsing
  const testJsonCorregido = testJson
    .replace(/"opcion([abcd])"/g, '"opcion_$1"')  // Corregir opcionX -> opcion_X
    .replace(/"opciona"/g, '"opcion_a"')
    .replace(/"opcionb"/g, '"opcion_b"')
    .replace(/"opcionc"/g, '"opcion_c"')
    .replace(/"opciond"/g, '"opcion_d"');

  const preguntas = JSON.parse(testJsonCorregido);

  // Verificar la cantidad de preguntas generadas
  console.log(`📊 Preguntas generadas: ${preguntas.length} de ${cantidad} solicitadas`);

  // Validar el formato
  if (!Array.isArray(preguntas) || preguntas.length === 0) {
    throw new Error("El formato de las preguntas generadas no es válido.");
  }

  if (preguntas.length !== cantidad) {
    console.log(`⚠️ Advertencia: Se generaron ${preguntas.length} preguntas en lugar de ${cantidad}`);
  }

  // Validar que cada pregunta tiene el formato correcto
  preguntas.forEach((pregunta: any, index: number) => {
    if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b ||
        !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {
      throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);
    }

    // Asegurarse de que la respuesta correcta es una de las opciones válidas
    if (!['a', 'b', 'c', 'd'].includes(pregunta.respuesta_correcta)) {
      throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);
    }
  });

  return preguntas;
}
