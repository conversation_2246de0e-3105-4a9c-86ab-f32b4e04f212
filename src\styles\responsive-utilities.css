/* Utilidades responsivas para la aplicación */

/* Espaciado responsivo */
.responsive-padding {
  @apply px-4 py-4 md:px-6 md:py-6;
}

.responsive-margin {
  @apply mx-4 my-4 md:mx-6 md:my-6;
}

.responsive-gap {
  @apply gap-3 md:gap-4 lg:gap-6;
}

/* Texto responsivo */
.responsive-text-sm {
  @apply text-sm md:text-base;
}

.responsive-text-base {
  @apply text-base md:text-lg;
}

.responsive-text-lg {
  @apply text-lg md:text-xl;
}

.responsive-text-xl {
  @apply text-xl md:text-2xl;
}

/* Utilidades de texto */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Contenedores responsivos */
.responsive-container {
  @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.responsive-grid-auto {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

.responsive-grid-2 {
  @apply grid grid-cols-1 md:grid-cols-2;
}

.responsive-grid-3 {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
}

.responsive-grid-4 {
  @apply grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4;
}

/* Flexbox responsivo */
.responsive-flex {
  @apply flex flex-col lg:flex-row;
}

.responsive-flex-reverse {
  @apply flex flex-col-reverse lg:flex-row;
}

/* Visibilidad responsiva */
.mobile-only {
  @apply block lg:hidden;
}

.desktop-only {
  @apply hidden lg:block;
}

.tablet-up {
  @apply hidden md:block;
}

.mobile-tablet {
  @apply block lg:hidden;
}

/* Botones responsivos */
.responsive-button {
  @apply px-3 py-2 text-sm md:px-4 md:py-2 md:text-base;
}

.responsive-button-lg {
  @apply px-4 py-3 text-base md:px-6 md:py-3 md:text-lg;
}

/* Cards responsivos */
.responsive-card {
  @apply bg-white rounded-lg md:rounded-xl shadow-sm border p-4 md:p-6;
}

.responsive-card-compact {
  @apply bg-white rounded-lg shadow-sm border p-3 md:p-4;
}

/* Navegación responsiva */
.responsive-nav-item {
  @apply px-3 py-2 text-sm md:px-4 md:py-2 md:text-base rounded-md transition-colors;
}

/* Formularios responsivos */
.responsive-input {
  @apply w-full px-3 py-2 text-sm md:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.responsive-label {
  @apply block text-sm md:text-base font-medium text-gray-700 mb-1 md:mb-2;
}

/* Modales responsivos */
.responsive-modal {
  @apply fixed inset-0 z-50 overflow-y-auto;
}

.responsive-modal-content {
  @apply bg-white rounded-lg md:rounded-xl shadow-xl mx-4 md:mx-auto md:max-w-lg lg:max-w-xl;
}

/* Sidebar responsivo */
.responsive-sidebar {
  @apply w-full lg:w-64 xl:w-72;
}

/* Header responsivo */
.responsive-header {
  @apply h-16 md:h-20;
}

/* Espaciado para contenido con bottom navigation */
.content-with-bottom-nav {
  @apply pb-16 lg:pb-0;
}

/* Animaciones responsivas */
@media (prefers-reduced-motion: no-preference) {
  .responsive-transition {
    @apply transition-all duration-200 ease-in-out;
  }
  
  .responsive-hover {
    @apply hover:scale-105 active:scale-95;
  }
}

/* Touch targets para móvil */
.touch-target {
  @apply min-h-[44px] min-w-[44px];
}

/* Scroll responsivo */
.responsive-scroll {
  @apply overflow-y-auto max-h-screen lg:max-h-[calc(100vh-theme(spacing.20))];
}

/* Utilidades de debug (solo en desarrollo) */
.debug-mobile {
  @apply bg-red-100 border border-red-300 lg:bg-transparent lg:border-transparent;
}

.debug-desktop {
  @apply bg-blue-100 border border-blue-300 lg:bg-green-100 lg:border-green-300;
}
