# Ejemplos Prácticos del Sistema de Chunking

## Casos de Uso Comunes

### 1. Procesamiento de Test con Documento Grande

```typescript
import { ChunkProcessingOrchestrator } from '@/lib/services/ChunkProcessingOrchestrator';

async function generarTestDesdeTemario(contenidoTemario: string) {
  const orchestrator = ChunkProcessingOrchestrator.getInstance();
  
  const config = {
    type: 'tests',
    contentType: 'temario',
    numQuestions: 50,
    difficulty: 'medium'
  };

  try {
    // El orquestador detecta automáticamente si necesita chunking
    const result = await orchestrator.processDocument(contenidoTemario, config);
    
    console.log(`Generadas ${result.combinedResult.questions.length} preguntas`);
    console.log(`Procesado en ${result.metadata.processingTime}ms`);
    
    return result.combinedResult;
  } catch (error) {
    console.error('Error generando test:', error);
    throw error;
  }
}
```

### 2. Validación Previa de Límites

```typescript
import { ChunkingLimitsValidator } from '@/lib/services/ChunkingLimitsValidator';

async function validarAntesDeProcesar(content: string, config: any) {
  const validator = new ChunkingLimitsValidator();
  
  const validation = await validator.validateProcessing(content, config);
  
  if (!validation.isValid) {
    console.log('❌ El documento excede los límites:');
    validation.violations.forEach(violation => {
      console.log(`- ${violation.type}: ${violation.message}`);
    });
    
    console.log('\n💡 Recomendaciones:');
    validation.recommendations.forEach(rec => {
      console.log(`- ${rec}`);
    });
    
    return false;
  }
  
  console.log('✅ Documento válido para procesamiento');
  return true;
}
```

### 3. Generación de Flashcards con Monitoreo

```typescript
import { ChunkProcessingOrchestrator } from '@/lib/services/ChunkProcessingOrchestrator';

async function generarFlashcardsConMonitoreo(content: string) {
  const orchestrator = ChunkProcessingOrchestrator.getInstance();
  
  const config = {
    type: 'flashcards',
    contentType: 'legal',
    numCards: 30
  };

  // Generar ID de sesión para tracking
  const sessionId = orchestrator.generateSessionId();
  
  console.log(`🚀 Iniciando procesamiento - Sesión: ${sessionId}`);
  
  try {
    const result = await orchestrator.processDocument(content, config);
    
    // Log de métricas finales
    console.log('📊 Métricas de procesamiento:');
    console.log(`- Chunks procesados: ${result.metadata.chunksProcessed || 1}`);
    console.log(`- Tiempo total: ${result.metadata.processingTime}ms`);
    console.log(`- Tokens estimados: ${result.metadata.estimatedTokens}`);
    console.log(`- Flashcards generadas: ${result.combinedResult.flashcards.length}`);
    
    return result.combinedResult;
  } catch (error) {
    console.error(`❌ Error en sesión ${sessionId}:`, error);
    throw error;
  }
}
```

### 4. Configuración Personalizada por Tipo de Documento

```typescript
import { getContentTypeConfig } from '@/config/chunking';

function configurarPorTipoDocumento(tipoDocumento: string) {
  const config = getContentTypeConfig(tipoDocumento);
  
  console.log(`Configuración para ${tipoDocumento}:`);
  console.log(`- Tamaño máximo de chunk: ${config.maxChunkSize} caracteres`);
  console.log(`- Solapamiento: ${config.overlapSize} caracteres`);
  console.log(`- Patrones de división: ${config.patterns.length} definidos`);
  
  return config;
}

// Ejemplos de uso
configurarPorTipoDocumento('temario');
configurarPorTipoDocumento('legal');
configurarPorTipoDocumento('tecnico');
```

### 5. Manejo de Errores Avanzado

```typescript
import { ChunkProcessingOrchestrator } from '@/lib/services/ChunkProcessingOrchestrator';

async function procesarConManejoDeErrores(content: string, config: any) {
  const orchestrator = ChunkProcessingOrchestrator.getInstance();
  
  try {
    const result = await orchestrator.processDocument(content, config);
    return { success: true, data: result.combinedResult };
  } catch (error) {
    if (error.name === 'ChunkingLimitExceededError') {
      return {
        success: false,
        error: 'LIMIT_EXCEEDED',
        message: 'El documento excede los límites de procesamiento',
        recommendations: error.recommendations
      };
    }
    
    if (error.name === 'ChunkProcessingError') {
      return {
        success: false,
        error: 'PROCESSING_ERROR',
        message: 'Error durante el procesamiento de chunks',
        partialResults: error.partialResults
      };
    }
    
    return {
      success: false,
      error: 'UNKNOWN_ERROR',
      message: error.message
    };
  }
}
```

### 6. Estimación de Tokens Precisa

```typescript
import { 
  estimateForTestsWithChunks,
  estimateForTests,
  estimateForFlashcardsWithChunks,
  estimateForFlashcards
} from '@/lib/services/tokenEstimationService';

async function estimarTokensCorrectamente(content: string, type: string, config: any) {
  const willUseChunking = content.length > 20000; // MIN_SIZE_FOR_CHUNKING
  
  let estimation;
  
  switch (type) {
    case 'tests':
      if (willUseChunking) {
        estimation = await estimateForTestsWithChunks(content, config.contentType);
      } else {
        estimation = await estimateForTests(content, config.numQuestions);
      }
      break;
      
    case 'flashcards':
      if (willUseChunking) {
        estimation = await estimateForFlashcardsWithChunks(content, config.contentType);
      } else {
        estimation = await estimateForFlashcards(content, config.numCards);
      }
      break;
  }
  
  console.log('📊 Estimación de tokens:');
  console.log(`- Input tokens: ${estimation.inputTokens}`);
  console.log(`- Output tokens: ${estimation.outputTokens}`);
  console.log(`- Total estimado: ${estimation.totalEstimated}`);
  console.log(`- Usará chunking: ${willUseChunking ? 'Sí' : 'No'}`);
  
  return estimation;
}
```

### 7. Logging Personalizado

```typescript
import { CHUNKING_LOGGING_CONFIG } from '@/config/chunking';

// Configurar logger personalizado
CHUNKING_LOGGING_CONFIG.customLogger = (level: string, message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    data,
    service: 'chunking-system'
  };
  
  // Enviar a sistema de logging externo
  if (level === 'error') {
    console.error('🔴 CHUNKING ERROR:', logEntry);
    // Enviar a Sentry, etc.
  } else if (level === 'warn') {
    console.warn('🟡 CHUNKING WARNING:', logEntry);
  } else {
    console.log('🔵 CHUNKING INFO:', logEntry);
  }
};
```

### 8. Test de Integración Personalizado

```typescript
import { ChunkProcessingOrchestrator } from '@/lib/services/ChunkProcessingOrchestrator';

describe('Mi Test de Chunking Personalizado', () => {
  it('debe procesar mi tipo de documento específico', async () => {
    const orchestrator = ChunkProcessingOrchestrator.getInstance();
    
    const contenidoPersonalizado = `
      # Mi Documento Específico
      
      ## Sección 1
      Contenido de la sección 1...
      
      ## Sección 2
      Contenido de la sección 2...
    `.repeat(1000); // Hacer documento grande
    
    const config = {
      type: 'tests',
      contentType: 'personalizado',
      numQuestions: 25
    };
    
    const result = await orchestrator.processDocument(contenidoPersonalizado, config);
    
    expect(result.combinedResult.questions).toHaveLength(25);
    expect(result.metadata.chunksProcessed).toBeGreaterThan(1);
    expect(result.metadata.processingTime).toBeLessThan(60000); // Menos de 1 minuto
  });
});
```

### 9. Monitoreo de Performance en Producción

```typescript
import { ChunkProcessingOrchestrator } from '@/lib/services/ChunkProcessingOrchestrator';

class ChunkingPerformanceMonitor {
  private metrics: Map<string, any[]> = new Map();
  
  async monitorProcessing(content: string, config: any) {
    const startTime = Date.now();
    const orchestrator = ChunkProcessingOrchestrator.getInstance();
    
    try {
      const result = await orchestrator.processDocument(content, config);
      
      const metrics = {
        timestamp: new Date(),
        contentLength: content.length,
        processingTime: Date.now() - startTime,
        chunksProcessed: result.metadata.chunksProcessed || 1,
        tokensEstimated: result.metadata.estimatedTokens,
        success: true,
        type: config.type,
        contentType: config.contentType
      };
      
      this.recordMetrics(metrics);
      return result;
    } catch (error) {
      const metrics = {
        timestamp: new Date(),
        contentLength: content.length,
        processingTime: Date.now() - startTime,
        success: false,
        error: error.message,
        type: config.type,
        contentType: config.contentType
      };
      
      this.recordMetrics(metrics);
      throw error;
    }
  }
  
  private recordMetrics(metrics: any) {
    const key = `${metrics.type}-${metrics.contentType}`;
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    this.metrics.get(key)!.push(metrics);
    
    // Mantener solo últimas 100 métricas
    if (this.metrics.get(key)!.length > 100) {
      this.metrics.get(key)!.shift();
    }
  }
  
  getPerformanceReport() {
    const report: any = {};
    
    for (const [key, metrics] of this.metrics) {
      const successfulMetrics = metrics.filter(m => m.success);
      
      if (successfulMetrics.length > 0) {
        report[key] = {
          totalProcessed: metrics.length,
          successRate: (successfulMetrics.length / metrics.length) * 100,
          avgProcessingTime: successfulMetrics.reduce((sum, m) => sum + m.processingTime, 0) / successfulMetrics.length,
          avgContentLength: successfulMetrics.reduce((sum, m) => sum + m.contentLength, 0) / successfulMetrics.length,
          avgChunksProcessed: successfulMetrics.reduce((sum, m) => sum + (m.chunksProcessed || 1), 0) / successfulMetrics.length
        };
      }
    }
    
    return report;
  }
}

// Uso del monitor
const monitor = new ChunkingPerformanceMonitor();

// En tu aplicación
const result = await monitor.monitorProcessing(content, config);

// Generar reporte periódico
setInterval(() => {
  const report = monitor.getPerformanceReport();
  console.log('📊 Reporte de Performance:', report);
}, 60000); // Cada minuto
```

## Mejores Prácticas

### 1. Validación Previa
Siempre valida los límites antes de procesar documentos grandes.

### 2. Manejo de Errores
Implementa manejo robusto de errores con recuperación parcial.

### 3. Monitoreo
Usa el sistema de logging para monitorear performance en producción.

### 4. Configuración
Ajusta la configuración según el tipo de contenido específico.

### 5. Testing
Crea tests específicos para tus casos de uso particulares.

---

Estos ejemplos muestran cómo aprovechar al máximo el sistema de chunking refactorizado en diferentes escenarios de uso.
