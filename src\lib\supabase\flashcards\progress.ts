/**
 * Servicio para gestión del progreso de flashcards
 * 
 * Responsabilidades:
 * - Gestión del progreso individual de flashcards
 * - Obtención de progreso y estadísticas
 * - Operaciones de reinicio de progreso
 */

import {
  supabase,
  ProgresoFlashcardDB
} from '../supabaseClient';

/**
 * Obtiene el progreso de una flashcard
 */
export async function obtenerProgresoFlashcard(flashcardId: string): Promise<ProgresoFlashcardDB | null> {
  const { data, error } = await supabase
    .from('progreso_flashcards')
    .select('*')
    .eq('flashcard_id', flashcardId)
    .maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro

  if (error) {
    console.error('Error al obtener progreso de flashcard:', error);
    return null;
  }

  return data || null;
}

/**
 * Reinicia el progreso de una flashcard
 */
export async function reiniciarProgresoFlashcard(flashcardId: string): Promise<boolean> {
  const { error } = await supabase
    .from('progreso_flashcards')
    .update({
      factor_facilidad: 2.5,
      intervalo: 0,
      repeticiones: 0,
      estado: 'nuevo',
      ultima_revision: new Date().toISOString(),
      proxima_revision: new Date().toISOString()
    })
    .eq('flashcard_id', flashcardId);

  if (error) {
    console.error('Error al reiniciar progreso de flashcard:', error);
    return false;
  }

  return true;
}
