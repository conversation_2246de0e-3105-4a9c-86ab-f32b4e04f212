// src/config/features.ts
// Configuración centralizada de características y funcionalidades

// ============================================================================
// CONSTANTES DE FEATURES
// ============================================================================

/**
 * Identificadores únicos de características del sistema
 */
export const FEATURE_IDS = {
  DOCUMENT_UPLOAD: 'document_upload',
  TEST_GENERATION: 'test_generation',
  FLASHCARD_GENERATION: 'flashcard_generation',
  MIND_MAP_GENERATION: 'mind_map_generation',
  AI_TUTOR_CHAT: 'ai_tutor_chat',
  STUDY_PLANNING: 'study_planning',
  SUMMARY_A1_A2: 'summary_a1_a2'
} as const;

/**
 * Tipo para los IDs de características
 */
export type FeatureId = typeof FEATURE_IDS[keyof typeof FEATURE_IDS];

/**
 * Acciones que pueden realizarse en el sistema
 */
export const ACTION_TYPES = {
  TEST_GENERATION: 'test_generation',
  FLASHCARD_GENERATION: 'flashcard_generation',
  MIND_MAP_GENERATION: 'mind_map_generation',
  AI_CHAT: 'ai_chat',
  STUDY_PLANNING: 'study_planning',
  SUMMARY_GENERATION: 'summary_generation'
} as const;

export type ActionType = typeof ACTION_TYPES[keyof typeof ACTION_TYPES];

// ============================================================================
// CONFIGURACIÓN DE FEATURES
// ============================================================================

/**
 * Configuración completa de una característica
 */
export interface FeatureConfig {
  id: FeatureId;
  name: string;
  displayName: string;
  description: string;
  category: 'core' | 'premium' | 'advanced';
  minimumPlans: string[];
  requiresPayment: boolean;
  tokensRequired: number;
  icon?: string;
  route?: string;
}

/**
 * Configuración de todas las características del sistema
 */
export const FEATURES_CONFIG: Record<FeatureId, FeatureConfig> = {
  [FEATURE_IDS.DOCUMENT_UPLOAD]: {
    id: FEATURE_IDS.DOCUMENT_UPLOAD,
    name: 'document_upload',
    displayName: 'Subida de documentos',
    description: 'Permite subir y procesar documentos PDF para estudio',
    category: 'core',
    minimumPlans: ['free', 'usuario', 'pro'],
    requiresPayment: false,
    tokensRequired: 0,
    icon: 'FiUpload',
    route: '/app'
  },
  [FEATURE_IDS.TEST_GENERATION]: {
    id: FEATURE_IDS.TEST_GENERATION,
    name: 'test_generation',
    displayName: 'Generación de tests',
    description: 'Genera tests automáticos basados en el contenido de estudio',
    category: 'core',
    minimumPlans: ['free', 'usuario', 'pro'],
    requiresPayment: false,
    tokensRequired: 5000,
    icon: 'FiFileText',
    route: '/app/tests'
  },
  [FEATURE_IDS.FLASHCARD_GENERATION]: {
    id: FEATURE_IDS.FLASHCARD_GENERATION,
    name: 'flashcard_generation',
    displayName: 'Generación de flashcards',
    description: 'Crea flashcards inteligentes para memorización efectiva',
    category: 'core',
    minimumPlans: ['free', 'usuario', 'pro'],
    requiresPayment: false,
    tokensRequired: 3000,
    icon: 'FiLayers',
    route: '/app/flashcards'
  },
  [FEATURE_IDS.MIND_MAP_GENERATION]: {
    id: FEATURE_IDS.MIND_MAP_GENERATION,
    name: 'mind_map_generation',
    displayName: 'Generación de mapas mentales',
    description: 'Genera mapas mentales visuales para mejor comprensión',
    category: 'core',
    minimumPlans: ['free', 'usuario', 'pro'],
    requiresPayment: false,
    tokensRequired: 4000,
    icon: 'FiGitBranch',
    route: '/app/mindmaps'
  },
  [FEATURE_IDS.AI_TUTOR_CHAT]: {
    id: FEATURE_IDS.AI_TUTOR_CHAT,
    name: 'ai_tutor_chat',
    displayName: 'Chat con preparador IA',
    description: 'Interactúa con un preparador de oposiciones inteligente',
    category: 'premium',
    minimumPlans: ['usuario', 'pro'],
    requiresPayment: true,
    tokensRequired: 2000,
    icon: 'FiMessageSquare',
    route: '/app/ai-tutor'
  },
  [FEATURE_IDS.STUDY_PLANNING]: {
    id: FEATURE_IDS.STUDY_PLANNING,
    name: 'study_planning',
    displayName: 'Planificación de estudios',
    description: 'Crea planes de estudio personalizados y estructurados',
    category: 'advanced',
    minimumPlans: ['pro'],
    requiresPayment: true,
    tokensRequired: 20000,
    icon: 'FiCalendar',
    route: '/plan-estudios'
  },
  [FEATURE_IDS.SUMMARY_A1_A2]: {
    id: FEATURE_IDS.SUMMARY_A1_A2,
    name: 'summary_a1_a2',
    displayName: 'Resúmenes A1 y A2',
    description: 'Genera resúmenes especializados para oposiciones A1 y A2',
    category: 'advanced',
    minimumPlans: ['pro'],
    requiresPayment: true,
    tokensRequired: 6000,
    icon: 'FiBook',
    route: '/app/summaries'
  }
};

// ============================================================================
// MAPEOS Y UTILIDADES
// ============================================================================

/**
 * Mapeo de acciones a características
 */
export const ACTION_TO_FEATURE_MAP: Record<ActionType, FeatureId> = {
  [ACTION_TYPES.TEST_GENERATION]: FEATURE_IDS.TEST_GENERATION,
  [ACTION_TYPES.FLASHCARD_GENERATION]: FEATURE_IDS.FLASHCARD_GENERATION,
  [ACTION_TYPES.MIND_MAP_GENERATION]: FEATURE_IDS.MIND_MAP_GENERATION,
  [ACTION_TYPES.AI_CHAT]: FEATURE_IDS.AI_TUTOR_CHAT,
  [ACTION_TYPES.STUDY_PLANNING]: FEATURE_IDS.STUDY_PLANNING,
  [ACTION_TYPES.SUMMARY_GENERATION]: FEATURE_IDS.SUMMARY_A1_A2
};

/**
 * Mapeo de actividades de tokens a características
 */
export const ACTIVITY_TO_FEATURE_MAP: Record<string, FeatureId> = {
  'test_generation': FEATURE_IDS.TEST_GENERATION,
  'flashcard_generation': FEATURE_IDS.FLASHCARD_GENERATION,
  'mind_map_generation': FEATURE_IDS.MIND_MAP_GENERATION,
  'ai_tutor_chat': FEATURE_IDS.AI_TUTOR_CHAT,
  'study_planning': FEATURE_IDS.STUDY_PLANNING,
  'summary_a1_a2': FEATURE_IDS.SUMMARY_A1_A2,
  'document_analysis': FEATURE_IDS.DOCUMENT_UPLOAD
};

/**
 * Configuración de rutas restringidas por plan
 */
export const PLAN_RESTRICTED_ROUTES: Record<string, string[]> = {
  '/plan-estudios': ['pro'],
  '/app/ai-tutor': ['usuario', 'pro'],
  '/app/summaries': ['pro'],
  '/app/advanced-features': ['pro']
};

// ============================================================================
// FUNCIONES UTILITARIAS
// ============================================================================

/**
 * Obtiene la configuración de una característica
 */
export function getFeatureConfig(featureId: FeatureId): FeatureConfig | undefined {
  return FEATURES_CONFIG[featureId];
}

/**
 * Obtiene el nombre para mostrar de una característica
 */
export function getFeatureDisplayName(featureId: string): string {
  const config = FEATURES_CONFIG[featureId as FeatureId];
  return config?.displayName || featureId;
}

/**
 * Obtiene todas las características de una categoría
 */
export function getFeaturesByCategory(category: 'core' | 'premium' | 'advanced'): FeatureConfig[] {
  return Object.values(FEATURES_CONFIG).filter(feature => feature.category === category);
}

/**
 * Obtiene las características disponibles para un plan
 */
export function getFeaturesForPlan(planId: string): FeatureConfig[] {
  return Object.values(FEATURES_CONFIG).filter(feature => 
    feature.minimumPlans.includes(planId)
  );
}

/**
 * Verifica si una característica requiere pago
 */
export function featureRequiresPayment(featureId: FeatureId): boolean {
  const config = getFeatureConfig(featureId);
  return config?.requiresPayment || false;
}

/**
 * Obtiene los tokens requeridos para una característica
 */
export function getFeatureTokensRequired(featureId: FeatureId): number {
  const config = getFeatureConfig(featureId);
  return config?.tokensRequired || 0;
}

/**
 * Convierte una acción a su característica correspondiente
 */
export function actionToFeature(action: ActionType): FeatureId {
  return ACTION_TO_FEATURE_MAP[action];
}

/**
 * Convierte una actividad a su característica correspondiente
 */
export function activityToFeature(activity: string): FeatureId | undefined {
  return ACTIVITY_TO_FEATURE_MAP[activity];
}

/**
 * Obtiene todas las características como array
 */
export function getAllFeatures(): FeatureConfig[] {
  return Object.values(FEATURES_CONFIG);
}

/**
 * Obtiene los IDs de todas las características
 */
export function getAllFeatureIds(): FeatureId[] {
  return Object.keys(FEATURES_CONFIG) as FeatureId[];
}

/**
 * Verifica si un ID de característica es válido
 */
export function isValidFeatureId(featureId: string): featureId is FeatureId {
  return featureId in FEATURES_CONFIG;
}
