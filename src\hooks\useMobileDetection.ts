'use client';

import { useState, useEffect } from 'react';

/**
 * Hook personalizado para detectar dispositivos móviles y gestionar breakpoints responsivos
 * Siguiendo los breakpoints de Tailwind CSS definidos en el proyecto
 */

interface MobileDetectionState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
}

const BREAKPOINTS = {
  mobile: 768,    // < 768px
  tablet: 1024,   // 768px - 1023px
  desktop: 1024,  // >= 1024px (lg: breakpoint)
} as const;

export const useMobileDetection = (): MobileDetectionState => {
  const [state, setState] = useState<MobileDetectionState>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenWidth: 1024, // Default para SSR
  });

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      
      setState({
        isMobile: width < BREAKPOINTS.mobile,
        isTablet: width >= BREAKPOINTS.mobile && width < BREAKPOINTS.tablet,
        isDesktop: width >= BREAKPOINTS.desktop,
        screenWidth: width,
      });
    };

    // Ejecutar inmediatamente para obtener el tamaño inicial
    updateScreenSize();

    // Agregar listener para cambios de tamaño
    window.addEventListener('resize', updateScreenSize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateScreenSize);
    };
  }, []);

  return state;
};

/**
 * Hook simplificado que solo retorna si es móvil o no
 * Útil para casos donde solo necesitamos saber si es móvil
 */
export const useIsMobile = (): boolean => {
  const { isMobile } = useMobileDetection();
  return isMobile;
};

/**
 * Hook que retorna si es desktop (lg: breakpoint de Tailwind)
 * Útil para mostrar/ocultar elementos según el breakpoint lg:
 */
export const useIsDesktop = (): boolean => {
  const { isDesktop } = useMobileDetection();
  return isDesktop;
};
