'use client';

import React, { useState, useEffect } from 'react';
import { useIsMobile, useMobileDetection } from '@/hooks/useMobileDetection';

interface ResponsiveDebuggerProps {
  enabled?: boolean;
}

const ResponsiveDebugger: React.FC<ResponsiveDebuggerProps> = ({ 
  enabled = process.env.NODE_ENV === 'development' 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });
  const isMobile = useIsMobile();
  const { isMobile: isMobileDetection, isTablet, isDesktop } = useMobileDetection();

  useEffect(() => {
    const updateSize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  if (!enabled) return null;

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-purple-600 text-white p-2 rounded-full shadow-lg hover:bg-purple-700 transition-colors"
        title="Toggle Responsive Debugger"
      >
        📱
      </button>

      {/* Debug panel */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-xl p-4 max-w-xs">
          <h3 className="font-bold text-sm mb-3 text-gray-900">Responsive Debug</h3>
          
          <div className="space-y-2 text-xs">
            <div>
              <strong>Window:</strong> {windowSize.width} × {windowSize.height}
            </div>
            
            <div>
              <strong>Device Type:</strong>
              <div className="ml-2">
                <div className={isMobileDetection ? 'text-green-600' : 'text-gray-400'}>
                  📱 Mobile: {isMobileDetection ? 'Yes' : 'No'}
                </div>
                <div className={isTablet ? 'text-green-600' : 'text-gray-400'}>
                  📟 Tablet: {isTablet ? 'Yes' : 'No'}
                </div>
                <div className={isDesktop ? 'text-green-600' : 'text-gray-400'}>
                  🖥️ Desktop: {isDesktop ? 'Yes' : 'No'}
                </div>
              </div>
            </div>

            <div>
              <strong>Breakpoints:</strong>
              <div className="ml-2">
                <div className={windowSize.width < 640 ? 'text-green-600' : 'text-gray-400'}>
                  SM: &lt;640px
                </div>
                <div className={windowSize.width >= 640 && windowSize.width < 768 ? 'text-green-600' : 'text-gray-400'}>
                  SM: 640px+
                </div>
                <div className={windowSize.width >= 768 && windowSize.width < 1024 ? 'text-green-600' : 'text-gray-400'}>
                  MD: 768px+
                </div>
                <div className={windowSize.width >= 1024 && windowSize.width < 1280 ? 'text-green-600' : 'text-gray-400'}>
                  LG: 1024px+
                </div>
                <div className={windowSize.width >= 1280 ? 'text-green-600' : 'text-gray-400'}>
                  XL: 1280px+
                </div>
              </div>
            </div>

            <div>
              <strong>Current Classes:</strong>
              <div className="ml-2 text-gray-600">
                <div className="block sm:hidden">Mobile Only</div>
                <div className="hidden sm:block md:hidden">SM Only</div>
                <div className="hidden md:block lg:hidden">MD Only</div>
                <div className="hidden lg:block xl:hidden">LG Only</div>
                <div className="hidden xl:block">XL Only</div>
              </div>
            </div>

            <div>
              <strong>Layout Status:</strong>
              <div className="ml-2">
                <div className="lg:hidden text-orange-600">Mobile Layout Active</div>
                <div className="hidden lg:block text-blue-600">Desktop Layout Active</div>
              </div>
            </div>
          </div>

          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              Resize window to test breakpoints
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ResponsiveDebugger;
