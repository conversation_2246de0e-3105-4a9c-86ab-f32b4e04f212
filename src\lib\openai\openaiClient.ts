import OpenAI from 'openai';
import { logTokenUsage, createOpenAITokenTracking } from '@/lib/ai/tokenTracker';
import * as Sentry from "@sentry/nextjs";
import { DocumentChunkingService } from '@/lib/services/DocumentChunkingService';
import { type Documento } from '@/types/database';
import { type Chunk } from '@/types/chunking';

/**
 * Resultado del procesamiento de documentos para IA
 */
export interface DocumentProcessingResult {
  /** Contenido procesado (string único o array de chunks) */
  content: string | string[];
  /** Indica si se usó chunking */
  wasChunked: boolean;
  /** Chunks originales si se usó chunking (para estimación de tokens) */
  chunks?: Chunk[];
  /** Información de estadísticas si está disponible */
  stats?: {
    totalChunks: number;
    totalContentSize: number;
    processingTimeMs: number;
    chunkingStrategy?: string;
  };
  /** Información del documento fuente para el orquestador */
  sourceDocument?: {
    id?: string;
    title: string;
    originalSize: number;
  };
}

// Configuración de OpenAI
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY no está configurada en las variables de entorno');
}

// Inicializar cliente de OpenAI
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});


export { openai };

/**
 * Trunca el contenido de un documento si es demasiado largo
 */
export function truncarContenido(contenido: string | undefined | null, maxLength: number = 25000): string {
  // Verificar que el contenido sea una cadena válida
  if (contenido === undefined || contenido === null) {
    console.warn('Se intentó truncar un contenido undefined o null');
    return '';
  }

  // Asegurarse de que el contenido sea una cadena
  const contenidoStr = String(contenido);

  if (contenidoStr.length <= maxLength) {
    return contenidoStr;
  }

  return contenidoStr.substring(0, maxLength) +
    `\n\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;
}

/**
 * Prepara los documentos para ser enviados a OpenAI
 * Ahora soporta chunking inteligente para documentos largos
 */
export function prepararDocumentos(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  usarChunking: boolean = true,
  contentType?: 'temario' | 'legal' | 'tecnico' | 'default'
): DocumentProcessingResult {
  if (!documentos || documentos.length === 0) {
    console.warn('No se proporcionaron documentos para preparar');
    return {
      content: '',
      wasChunked: false
    };
  }

  // Si el chunking está habilitado y es un solo documento, usar el servicio de chunking
  if (usarChunking && documentos.length === 1) {
    const documento = documentos[0];

    // Convertir al formato esperado por DocumentChunkingService
    const documentoParaChunking: Documento = {
      id: `temp_${Date.now()}`,
      titulo: documento.titulo,
      contenido: documento.contenido,
      categoria: documento.categoria || 'General',
      numero_tema: documento.numero_tema || 1,
      creado_en: new Date().toISOString(),
      actualizado_en: new Date().toISOString(),
      user_id: 'temp_user'
    };

    try {
      const resultado = DocumentChunkingService.processDocument(documentoParaChunking, {
        enableChunking: true,
        contentType: contentType || 'default',
        includeStats: true
      });

      if (resultado.wasChunked && Array.isArray(resultado.content)) {
        // Retornar array de strings de chunks para procesamiento múltiple
        const formattedChunks = resultado.content.map(chunk => formatearChunkParaIA(chunk, documento));
        return {
          content: formattedChunks,
          wasChunked: true,
          chunks: resultado.content,
          stats: resultado.stats ? {
            totalChunks: resultado.stats.totalChunks,
            totalContentSize: resultado.stats.totalContentSize,
            processingTimeMs: resultado.stats.processingTimeMs,
            chunkingStrategy: resultado.stats.chunkingStrategy
          } : undefined,
          sourceDocument: resultado.sourceDocument
        };
      } else {
        // Documento pequeño, usar contenido directo
        return {
          content: formatearDocumentoSimple(documento),
          wasChunked: false,
          sourceDocument: {
            id: documentoParaChunking.id,
            title: documento.titulo,
            originalSize: documento.contenido.length
          }
        };
      }
    } catch (error) {
      console.warn('Error en chunking, usando método tradicional:', error);
      // Fallback al método tradicional - continuar con el procesamiento normal
    }
  }

  // Método tradicional para múltiples documentos o cuando chunking está deshabilitado
  const documentosProcesados = documentos.map((doc, index) => {
    // Verificar que el documento tenga contenido válido
    if (!doc.contenido || typeof doc.contenido !== 'string') {
      console.warn(`Documento ${index + 1} (${doc.titulo || 'Sin título'}) no tiene contenido válido`);
      return null;
    }

    // Truncar el contenido si es necesario (solo si no se usa chunking)
    const contenidoFinal = usarChunking ? doc.contenido : truncarContenido(doc.contenido, 15000);

    return {
      titulo: doc.titulo || `Documento ${index + 1}`,
      contenido: contenidoFinal,
      categoria: doc.categoria || 'General',
      numero_tema: doc.numero_tema || index + 1
    };
  }).filter(doc => doc !== null);

  if (documentosProcesados.length === 0) {
    console.warn('No se pudieron procesar documentos válidos');
    return {
      content: '',
      wasChunked: false
    };
  }

  // Crear el contexto combinado
  const contexto = documentosProcesados.map(doc => {
    return `
=== DOCUMENTO: ${doc.titulo} ===
Categoría: ${doc.categoria}
Tema: ${doc.numero_tema}

${doc.contenido}

=== FIN DOCUMENTO: ${doc.titulo} ===
`;
  }).join('\n\n');

  // Verificar longitud total y truncar si es necesario (solo para múltiples documentos)
  const MAX_TOTAL_LENGTH = 50000;
  let finalContent = contexto;
  if (contexto.length > MAX_TOTAL_LENGTH) {
    console.warn(`El contexto total (${contexto.length} caracteres) excede el límite de ${MAX_TOTAL_LENGTH}. Se truncará.`);
    finalContent = contexto.substring(0, MAX_TOTAL_LENGTH) +
      `\n\n[CONTEXTO TRUNCADO: El contenido total excedió el límite de ${MAX_TOTAL_LENGTH} caracteres.]`;
  }

  return {
    content: finalContent,
    wasChunked: false,
    sourceDocument: {
      id: `multi_${Date.now()}`,
      title: documentosProcesados.length === 1
        ? documentosProcesados[0].titulo
        : `${documentosProcesados.length} documentos`,
      originalSize: contexto.length
    }
  };
}

/**
 * Formatea un chunk para ser procesado por IA
 */
function formatearChunkParaIA(
  chunk: Chunk,
  documentoOriginal: { titulo: string; categoria?: string; numero_tema?: number }
): string {
  const { content, metadata } = chunk;

  return `
=== DOCUMENTO: ${documentoOriginal.titulo} (Fragmento ${metadata.chunkNumber}/${metadata.totalChunks}) ===
Categoría: ${documentoOriginal.categoria || 'General'}
Tema: ${documentoOriginal.numero_tema || 1}
Fragmento: ${metadata.chunkNumber} de ${metadata.totalChunks}
Tipo de división: ${metadata.chunkType}
${metadata.detectedSections.length > 0 ? `Secciones detectadas: ${metadata.detectedSections.slice(0, 3).join(', ')}${metadata.detectedSections.length > 3 ? '...' : ''}` : ''}

${content}

=== FIN FRAGMENTO ${metadata.chunkNumber}/${metadata.totalChunks}: ${documentoOriginal.titulo} ===
`;
}

/**
 * Formatea un documento simple (sin chunking)
 */
function formatearDocumentoSimple(documento: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }): string {
  return `
=== DOCUMENTO: ${documento.titulo} ===
Categoría: ${documento.categoria || 'General'}
Tema: ${documento.numero_tema || 1}

${documento.contenido}

=== FIN DOCUMENTO: ${documento.titulo} ===
`;
}

/**
 * Función auxiliar para hacer llamadas a OpenAI con manejo de errores
 */
export async function llamarOpenAI(
  messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
  options: {
    model?: string;
    temperature?: number;
    max_tokens?: number;
    activityName?: string; // Para tracking de tokens
    estimatedTokens?: number; // Para comparar estimación vs consumo real
    estimationType?: string; // Tipo de estimación para logging
    userId?: string; // ID del usuario para logging
  } = {}
): Promise<string> {
  const {
    model, // SIN modelo por defecto - debe ser especificado siempre
    temperature = 0.7,
    max_tokens = 4000,
    activityName = 'OpenAI Call',
    estimatedTokens,
    estimationType,
    userId
  } = options;



  // Validar que siempre se proporcione un modelo específico
  if (!model) {
    console.error(`❌ [OPENAI_CLIENT] Modelo no especificado`);
    throw new Error('❌ MODELO REQUERIDO: Debe especificarse un modelo específico para cada tarea. No se permite usar modelo por defecto.');
  }

  try {


    // Detectar si es un modelo de razonamiento (serie "o")
    const isReasoningModel = model.includes('o1') || model.includes('o3') || model.includes('o4') || model.startsWith('o');

    // Configurar parámetros según el tipo de modelo
    const requestParams: any = {
      model,
      messages,
    };

    if (isReasoningModel) {
      // Modelos de razonamiento: parámetros específicos
      requestParams.max_completion_tokens = max_tokens;
      // Los modelos de razonamiento solo soportan temperature = 1 (por defecto)
      // No agregamos temperature para usar el valor por defecto

    } else {
      // Modelos tradicionales: parámetros estándar
      requestParams.max_tokens = max_tokens;
      requestParams.temperature = temperature;
    }

    const completion = await openai.chat.completions.create(requestParams);

    const respuesta = completion.choices[0]?.message?.content;

    if (!respuesta) {
      console.error(`❌ [OPENAI_CLIENT] OpenAI no devolvió respuesta válida:`, {
        choices: completion.choices,
        choicesLength: completion.choices?.length
      });
      throw new Error('OpenAI no devolvió una respuesta válida');
    }

    // Tracking de tokens para OpenAI
    if (completion.usage) {
      const trackingData = createOpenAITokenTracking(
        activityName,
        model,
        completion.usage
      );
      logTokenUsage(trackingData);

      // Logging de precisión de estimación si se proporcionó
      if (estimatedTokens && estimationType && completion.usage.total_tokens) {
        // Importar dinámicamente para evitar dependencias circulares
        const { TokenEstimationService } = await import('@/lib/services/tokenEstimationService');
        TokenEstimationService.logEstimationAccuracy(
          estimationType as any,
          estimatedTokens,
          completion.usage.total_tokens,
          userId
        );
      }
    }

    return respuesta;

  } catch (error: any) {
    // ---> INICIO DEL CÓDIGO A AÑADIR <---
    Sentry.captureException(error, {
      tags: { section: "openai-client" },
      extra: {
        context: "Error calling OpenAI API.",
        model,
        errorCode: error.code,
        errorType: error.constructor?.name,
        timestamp: new Date().toISOString()
      },
    });
    // ---> FIN DEL CÓDIGO A AÑADIR <---

    console.error(`💥 [OPENAI_CLIENT] Error al llamar a OpenAI:`, {
      errorMessage: error.message,
      errorCode: error.code,
      errorType: error.constructor?.name,
      timestamp: new Date().toISOString()
    });

    if (error.code === 'insufficient_quota') {
      throw new Error('Cuota de OpenAI agotada. Por favor, verifica tu plan de facturación.');
    } else if (error.code === 'invalid_api_key') {
      throw new Error('API Key de OpenAI inválida. Verifica tu configuración.');
    } else if (error.code === 'model_not_found') {
      // Fallbacks para modelos no disponibles
      if (model.includes('gpt-4.1') || model.includes('o1') || model.includes('o3') || model.includes('o4')) {
        return await llamarOpenAI(messages, { ...options, model: 'gpt-4o' });
      } else if (model.includes('gpt-4o') && !model.includes('mini')) {
        return await llamarOpenAI(messages, { ...options, model: 'gpt-4o-mini' });
      }
      throw new Error(`Modelo ${model} no encontrado. Verifica que tienes acceso a este modelo.`);
    }

    throw new Error(`Error de OpenAI: ${error.message || 'Error desconocido'}`);
  }
}

/**
 * Función de utilidad para preparar documentos con configuración simplificada
 * Especialmente útil para integración con ChunkProcessingOrchestrator
 */
export function prepararDocumentosParaOrquestador(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  options: {
    enableChunking?: boolean;
    contentType?: 'temario' | 'legal' | 'tecnico' | 'default';
    forceChunking?: boolean;
  } = {}
): DocumentProcessingResult {
  const { enableChunking = true, contentType = 'default', forceChunking = false } = options;

  // Si se fuerza el chunking, usar siempre chunking independientemente del tamaño
  if (forceChunking && documentos.length === 1) {
    const documento = documentos[0];
    const documentoParaChunking: Documento = {
      id: `temp_${Date.now()}`,
      titulo: documento.titulo,
      contenido: documento.contenido,
      categoria: documento.categoria || 'General',
      numero_tema: documento.numero_tema || 1,
      creado_en: new Date().toISOString(),
      actualizado_en: new Date().toISOString(),
      user_id: 'temp_user'
    };

    const resultado = DocumentChunkingService.processDocument(documentoParaChunking, {
      enableChunking: true,
      contentType,
      forceChunking: true,
      includeStats: true
    });

    if (Array.isArray(resultado.content)) {
      const formattedChunks = resultado.content.map(chunk => formatearChunkParaIA(chunk, documento));
      return {
        content: formattedChunks,
        wasChunked: true,
        chunks: resultado.content,
        stats: resultado.stats ? {
          totalChunks: resultado.stats.totalChunks,
          totalContentSize: resultado.stats.totalContentSize,
          processingTimeMs: resultado.stats.processingTimeMs,
          chunkingStrategy: resultado.stats.chunkingStrategy
        } : undefined,
        sourceDocument: resultado.sourceDocument
      };
    }
  }

  // Usar la función estándar para otros casos
  return prepararDocumentos(documentos, enableChunking, contentType);
}
