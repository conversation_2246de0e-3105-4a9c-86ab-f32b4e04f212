import { prepararDocumentos } from './geminiClient';
import { PROMPT_PREGUNTAS } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { TokenEstimationService } from '../services/tokenEstimationService';

/**
 * Límite de tokens para chat (conservador para evitar errores de rate limit)
 */
const MAX_CHAT_TOKENS = 25000;

/**
 * Obtiene una respuesta de la IA a una pregunta sobre los documentos
 * Maneja automáticamente el chunking para evitar límites de tokens
 */
export async function obtenerRespuestaIA(
  pregunta: string,
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<string> {
  try {
    // Verificar que la pregunta sea válida
    if (!pregunta || typeof pregunta !== 'string' || pregunta.trim() === '') {
      console.warn('Se recibió una pregunta vacía o inválida');
      return "Por favor, proporciona una pregunta válida.";
    }

    // Verificar que los documentos sean válidos
    if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {
      console.warn('No se proporcionaron documentos válidos para obtenerRespuestaIA');
      return "No se han proporcionado documentos para responder a esta pregunta.";
    }

    // Preparar el contenido de los documentos con chunking inteligente
    const resultadoDocumentos = prepararDocumentos(documentos, true, 'temario'); // Forzar chunking para documentos grandes

    // Obtener configuración específica para conversaciones
    const config = getOpenAIConfig('CONVERSACIONES');
    console.log(`💬 Generando respuesta con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

    // Si el documento fue chunkeado, procesar de manera inteligente
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      console.log(`🧩 Documento chunkeado en ${resultadoDocumentos.content.length} partes. Procesando de manera inteligente...`);

      return await procesarPreguntaConChunks(pregunta, resultadoDocumentos.content, config);
    } else {
      // Procesamiento tradicional para documentos pequeños
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos) {
        console.warn('No se pudo preparar el contenido de los documentos');
        return "No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan información válida.";
      }

      // Verificar que no exceda límites de tokens
      const estimacion = TokenEstimationService.estimateForChat([contenidoDocumentos]);
      if (estimacion.totalEstimated > MAX_CHAT_TOKENS) {
        console.warn(`⚠️ Contenido demasiado grande (${estimacion.totalEstimated} tokens), forzando chunking`);
        // Forzar chunking si el contenido es demasiado grande
        const resultadoForzado = prepararDocumentos(documentos, true, 'temario');
        if (Array.isArray(resultadoForzado.content)) {
          return await procesarPreguntaConChunks(pregunta, resultadoForzado.content, config);
        }
      }

      // Construir el prompt para la IA
      const prompt = PROMPT_PREGUNTAS
        .replace('{documentos}', contenidoDocumentos)
        .replace('{pregunta}', pregunta);

      // Generar la respuesta usando OpenAI
      const messages = [{ role: 'user' as const, content: prompt }];
      return await llamarOpenAI(messages, {
        ...config,
        activityName: 'Conversación/Q&A'
      });
    }
  } catch (error) {
    console.error('Error al obtener respuesta de la IA:', error);

    // Proporcionar un mensaje de error más descriptivo si es posible
    if (error instanceof Error) {
      return `Lo siento, ha ocurrido un error al procesar tu pregunta: ${error.message}. Por favor, inténtalo de nuevo más tarde.`;
    }

    return "Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo más tarde.";
  }
}

/**
 * Procesa una pregunta usando chunks de manera inteligente
 * Busca en chunks relevantes y combina las respuestas
 */
async function procesarPreguntaConChunks(
  pregunta: string,
  chunks: string[],
  config: any
): Promise<string> {
  try {
    console.log(`🔍 Buscando información relevante en ${chunks.length} chunks...`);

    // Estrategia 1: Buscar chunks que contengan palabras clave de la pregunta
    const palabrasClave = extraerPalabrasClave(pregunta);
    const chunksRelevantes = encontrarChunksRelevantes(chunks, palabrasClave);

    console.log(`📋 Encontrados ${chunksRelevantes.length} chunks relevantes de ${chunks.length} totales`);

    // Si encontramos chunks relevantes, usar solo esos
    if (chunksRelevantes.length > 0) {
      const contenidoRelevante = chunksRelevantes.join('\n\n');

      // Verificar que no exceda límites
      const estimacion = TokenEstimationService.estimateForChat([contenidoRelevante]);
      if (estimacion.totalEstimated <= MAX_CHAT_TOKENS) {
        console.log(`✅ Usando ${chunksRelevantes.length} chunks relevantes (${estimacion.totalEstimated} tokens estimados)`);

        const prompt = PROMPT_PREGUNTAS
          .replace('{documentos}', contenidoRelevante)
          .replace('{pregunta}', pregunta);

        const messages = [{ role: 'user' as const, content: prompt }];
        return await llamarOpenAI(messages, {
          ...config,
          activityName: `Conversación/Q&A - ${chunksRelevantes.length} chunks relevantes`
        });
      }
    }

    // Estrategia 2: Si hay demasiados chunks relevantes o no encontramos ninguno,
    // usar los primeros chunks hasta el límite de tokens
    console.log(`⚠️ Usando estrategia de fallback: primeros chunks hasta límite de tokens`);

    let contenidoAcumulado = '';
    let chunksUsados = 0;

    for (const chunk of chunks) {
      const contenidoTemporal = contenidoAcumulado + (contenidoAcumulado ? '\n\n' : '') + chunk;
      const estimacion = TokenEstimationService.estimateForChat([contenidoTemporal]);

      if (estimacion.totalEstimated > MAX_CHAT_TOKENS) {
        break;
      }

      contenidoAcumulado = contenidoTemporal;
      chunksUsados++;
    }

    if (!contenidoAcumulado) {
      // Si ni siquiera el primer chunk cabe, usar solo una parte
      contenidoAcumulado = chunks[0].substring(0, 15000); // Truncar a ~15k caracteres
      chunksUsados = 1;
    }

    console.log(`📄 Usando ${chunksUsados} chunks (de ${chunks.length}) para responder`);

    const prompt = PROMPT_PREGUNTAS
      .replace('{documentos}', contenidoAcumulado)
      .replace('{pregunta}', pregunta);

    const messages = [{ role: 'user' as const, content: prompt }];
    const respuesta = await llamarOpenAI(messages, {
      ...config,
      activityName: `Conversación/Q&A - ${chunksUsados}/${chunks.length} chunks`
    });

    // Si no usamos todos los chunks, agregar una nota
    if (chunksUsados < chunks.length) {
      return respuesta + `\n\n*Nota: Esta respuesta se basa en ${chunksUsados} de ${chunks.length} secciones del documento. Si necesitas información de otras secciones específicas, por favor hazme una pregunta más específica.*`;
    }

    return respuesta;

  } catch (error) {
    console.error('Error procesando pregunta con chunks:', error);
    throw error;
  }
}

/**
 * Extrae palabras clave relevantes de una pregunta
 */
function extraerPalabrasClave(pregunta: string): string[] {
  // Convertir a minúsculas y eliminar caracteres especiales
  const preguntaLimpia = pregunta.toLowerCase()
    .replace(/[^\w\sáéíóúñü]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Palabras comunes a filtrar
  const palabrasComunes = new Set([
    'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas',
    'de', 'del', 'en', 'con', 'por', 'para', 'que', 'se',
    'es', 'son', 'está', 'están', 'como', 'qué', 'cuál',
    'explícame', 'explica', 'dime', 'cuéntame', 'háblame',
    'sobre', 'acerca', 'respecto', 'tema', 'apartado', 'sección'
  ]);

  // Extraer palabras de 3+ caracteres que no sean comunes
  const palabras = preguntaLimpia.split(' ')
    .filter(palabra => palabra.length >= 3 && !palabrasComunes.has(palabra));

  // Buscar también números de apartados/secciones
  const numerosApartados = pregunta.match(/\d+\.?\d*/g) || [];

  return [...palabras, ...numerosApartados];
}

/**
 * Encuentra chunks relevantes basándose en palabras clave
 */
function encontrarChunksRelevantes(chunks: string[], palabrasClave: string[]): string[] {
  if (palabrasClave.length === 0) {
    return [];
  }

  const chunksConPuntuacion = chunks.map((chunk, index) => {
    let puntuacion = 0;
    const chunkLowerCase = chunk.toLowerCase();

    // Contar coincidencias de palabras clave
    for (const palabra of palabrasClave) {
      const regex = new RegExp(`\\b${palabra.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'gi');
      const coincidencias = (chunkLowerCase.match(regex) || []).length;
      puntuacion += coincidencias;
    }

    return { chunk, index, puntuacion };
  });

  // Filtrar chunks con al menos una coincidencia y ordenar por puntuación
  const chunksRelevantes = chunksConPuntuacion
    .filter(item => item.puntuacion > 0)
    .sort((a, b) => b.puntuacion - a.puntuacion)
    .slice(0, 10) // Máximo 10 chunks más relevantes
    .sort((a, b) => a.index - b.index) // Mantener orden original
    .map(item => item.chunk);

  return chunksRelevantes;
}
