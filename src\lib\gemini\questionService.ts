import { prepararDocumentos } from './geminiClient';
import { PROMPT_PREGUNTAS } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';

/**
 * Obtiene una respuesta de la IA a una pregunta sobre los documentos
 */
export async function obtenerRespuestaIA(
  pregunta: string,
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<string> {
  try {
    // Verificar que la pregunta sea válida
    if (!pregunta || typeof pregunta !== 'string' || pregunta.trim() === '') {
      console.warn('Se recibió una pregunta vacía o inválida');
      return "Por favor, proporciona una pregunta válida.";
    }

    // Verificar que los documentos sean válidos
    if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {
      console.warn('No se proporcionaron documentos válidos para obtenerRespuestaIA');
      return "No se han proporcionado documentos para responder a esta pregunta.";
    }

    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      console.warn('No se pudo preparar el contenido de los documentos');
      return "No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan información válida.";
    }

    // Construir el prompt para la IA usando el prompt personalizado
    // Reemplazar las variables en el prompt
    const prompt = PROMPT_PREGUNTAS
      .replace('{documentos}', contenidoDocumentos)
      .replace('{pregunta}', pregunta);

    // Obtener configuración específica para conversaciones
    const config = getOpenAIConfig('CONVERSACIONES');

    console.log(`💬 Generando respuesta con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

    // Generar la respuesta usando OpenAI con la configuración correcta
    const messages = [{ role: 'user' as const, content: prompt }];
    return await llamarOpenAI(messages, {
      ...config,
      activityName: 'Conversación/Q&A'
    });
  } catch (error) {
    console.error('Error al obtener respuesta de la IA:', error);

    // Proporcionar un mensaje de error más descriptivo si es posible
    if (error instanceof Error) {
      return `Lo siento, ha ocurrido un error al procesar tu pregunta: ${error.message}. Por favor, inténtalo de nuevo más tarde.`;
    }

    return "Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo más tarde.";
  }
}
