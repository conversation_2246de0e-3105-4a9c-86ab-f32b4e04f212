import { prepararDocumentos } from './geminiClient';
import { PROMPT_FLASHCARDS } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { FlashcardResultCombiner } from '@/lib/services/ResultCombinerService';
import { type Chunk } from '@/lib/utils/textProcessing';
import { seleccionarChunksRelevantes } from '@/lib/utils/chunkSelector';

/**
 * Genera flashcards a partir de los documentos
 */
export async function generarFlashcards(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{ pregunta: string; respuesta: string }[]> {
  try {
    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);

    // Verificar si se usó chunking
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      console.log(`🃏 Procesando flashcards con chunking: ${resultadoDocumentos.content.length} chunks`);
      return await procesarFlashcardsConChunks(resultadoDocumentos.content, cantidad, instrucciones, documentos);
    } else {
      // Procesamiento tradicional para documentos sin chunking
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos) {
        throw new Error("No se han proporcionado documentos para generar flashcards.");
      }

      console.log(`🃏 Procesando flashcards sin chunking`);
      return await procesarFlashcardsSinChunks(contenidoDocumentos, cantidad, instrucciones);
    }


  } catch (error) {
    console.error('Error al generar flashcards:', error);
    throw error;
  }
}

/**
 * Procesa flashcards con chunking - selecciona chunks relevantes y los procesa individualmente
 */
async function procesarFlashcardsConChunks(
  chunks: Chunk[],
  cantidad: number,
  instrucciones?: string,
  documentos?: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<{ pregunta: string; respuesta: string }[]> {
  const resultadosChunks: any[][] = [];
  const chunkingContexts: any[] = [];

  // --- INICIO DE LA NUEVA LÓGICA DE SELECCIÓN ---
  console.log(`🃏 Seleccionando chunks relevantes para la petición: "${instrucciones}"`);
  const selectionResult = seleccionarChunksRelevantes(chunks, instrucciones || '');

  if (selectionResult.selectedChunks.length === 0) {
    throw new Error("No se pudo encontrar contenido relacionado con tu petición. Intenta ser más específico.");
  }

  console.log(`🃏 Chunks relevantes encontrados: ${selectionResult.selectedChunks.length} de ${chunks.length}`);
  // --- FIN DE LA NUEVA LÓGICA DE SELECCIÓN ---

  // MODIFICACIÓN: Iterar sobre los chunks relevantes en lugar de todos
  for (let i = 0; i < selectionResult.selectedChunks.length; i++) {
    const chunkObj = selectionResult.selectedChunks[i];
    const chunkContent = chunkObj.content; // Usar el contenido del objeto Chunk
    console.log(`🃏 Procesando chunk relevante ${i + 1}/${selectionResult.selectedChunks.length}`);

    // MODIFICACIÓN: Calcular cantidad proporcional sobre los chunks relevantes
    const cantidadPorChunk = Math.ceil(cantidad / selectionResult.selectedChunks.length);

    // Construir prompt para este chunk específico
    let promptChunk = PROMPT_FLASHCARDS
      .replace('{documentos}', chunkContent) // Usar chunkContent
      .replace('{cantidad}', cantidadPorChunk.toString());

    // Añadir instrucciones adicionales si se proporcionan
    if (instrucciones) {
      promptChunk = promptChunk.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
    } else {
      promptChunk = promptChunk.replace('{instrucciones}', '');
    }

    // Obtener configuración específica para flashcards
    const config = getOpenAIConfig('FLASHCARDS');

    // Generar flashcards para este chunk
    const messages = [{ role: 'user' as const, content: promptChunk }];
    const responseText = await llamarOpenAI(messages, {
      ...config,
      activityName: `Generación de Flashcards - Chunk ${i + 1}/${selectionResult.selectedChunks.length} (${cantidadPorChunk} tarjetas)`
    });

    // Extraer el JSON de la respuesta
    const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

    if (!jsonMatch) {
      console.warn(`No se pudo extraer JSON del chunk ${i + 1}, continuando con el siguiente`);
      continue;
    }

    try {
      const flashcardsChunk = JSON.parse(jsonMatch[0]);

      // Validar formato básico
      if (Array.isArray(flashcardsChunk) && flashcardsChunk.length > 0) {
        resultadosChunks.push(flashcardsChunk);

        // Guardar contexto del chunk para el combinador
        chunkingContexts.push({
          chunkIndex: i, // Usamos el índice del bucle de chunks relevantes
          chunkSize: chunkContent.length,
          flashcardsGenerated: flashcardsChunk.length
        });
      }
    } catch (parseError) {
      console.warn(`Error parseando JSON del chunk ${i + 1}:`, parseError);
      continue;
    }
  }

  // Combinar resultados usando ResultCombinerService
  if (resultadosChunks.length > 0) {
    const combinedResult = FlashcardResultCombiner.combine(
      resultadosChunks,
      cantidad,
      chunkingContexts,
      {
        id: documentos?.[0]?.titulo || 'documento',
        title: documentos?.[0]?.titulo || 'Documento sin título',
        totalSize: chunks.reduce((total, chunk) => total + chunk.length, 0)
      }
    );

    console.log(`🃏 Flashcards generadas con chunking: ${combinedResult.combinedResult.length} tarjetas finales`);
    return combinedResult.combinedResult;
  } else {
    throw new Error("No se pudieron generar flashcards de ningún chunk");
  }
}

/**
 * Procesa flashcards sin chunking - método tradicional
 */
async function procesarFlashcardsSinChunks(
  contenidoDocumentos: string,
  cantidad: number,
  instrucciones?: string
): Promise<{ pregunta: string; respuesta: string }[]> {
  // Construir el prompt para la IA usando el prompt personalizado
  let prompt = PROMPT_FLASHCARDS
    .replace('{documentos}', contenidoDocumentos)
    .replace('{cantidad}', cantidad.toString());

  // Añadir instrucciones adicionales si se proporcionan
  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // Obtener configuración específica para flashcards
  const config = getOpenAIConfig('FLASHCARDS');

  console.log(`🃏 Generando flashcards con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

  // Generar las flashcards usando OpenAI con la configuración correcta
  const messages = [{ role: 'user' as const, content: prompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: `Generación de Flashcards (${cantidad || 'N/A'} tarjetas)`
  });

  // Extraer el JSON de la respuesta
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

  if (!jsonMatch) {
    throw new Error("No se pudo extraer el formato JSON de la respuesta.");
  }

  const flashcardsJson = jsonMatch[0];
  const flashcards = JSON.parse(flashcardsJson);

  // Validar el formato
  if (!Array.isArray(flashcards) || flashcards.length === 0) {
    throw new Error("El formato de las flashcards generadas no es válido.");
  }

  return flashcards;
}
