'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useBackgroundTasks, BackgroundTask } from '@/contexts/BackgroundTasksContext';

interface UseTaskResultsOptions {
  taskType: BackgroundTask['type'];
  onResult?: (result: any) => void;
  onError?: (error: string) => void;
}

export const useTaskResults = ({ taskType, onResult, onError }: UseTaskResultsOptions) => {
  const { tasks, isInitialized } = useBackgroundTasks();
  const [processedTaskIds, setProcessedTaskIds] = useState<Set<string>>(new Set());
  const [hasCheckedInitialTasks, setHasCheckedInitialTasks] = useState(false);

  // Usar refs para los callbacks para evitar dependencias que cambien en cada render
  const onResultRef = useRef(onResult);
  const onErrorRef = useRef(onError);

  // Actualizar las refs cuando cambien los callbacks
  onResultRef.current = onResult;
  onErrorRef.current = onError;

  // Efecto para verificar tareas iniciales cuando el contexto se inicializa
  useEffect(() => {
    if (!isInitialized || hasCheckedInitialTasks) return;

    // Buscar tareas completadas que podrían haberse perdido durante la navegación
    const missedCompletedTasks = tasks.filter(task =>
      task.type === taskType &&
      task.status === 'completed' &&
      task.result &&
      !processedTaskIds.has(task.id)
    );

    const missedErrorTasks = tasks.filter(task =>
      task.type === taskType &&
      task.status === 'error' &&
      task.error &&
      !processedTaskIds.has(task.id)
    );

    // Procesar la tarea más reciente si existe
    if (missedCompletedTasks.length > 0) {
      const latestTask = missedCompletedTasks
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];

      console.log(`🔄 Recuperando resultado de tarea perdida: ${latestTask.id}`);

      setProcessedTaskIds(prev => {
        const newSet = new Set(prev);
        newSet.add(latestTask.id);
        return newSet;
      });

      if (onResultRef.current) {
        setTimeout(() => {
          onResultRef.current?.(latestTask.result);
        }, 0);
      }
    } else if (missedErrorTasks.length > 0) {
      const latestErrorTask = missedErrorTasks
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];

      console.log(`❌ Recuperando error de tarea perdida: ${latestErrorTask.id}`);

      setProcessedTaskIds(prev => {
        const newSet = new Set(prev);
        newSet.add(latestErrorTask.id);
        return newSet;
      });

      if (onErrorRef.current) {
        setTimeout(() => {
          onErrorRef.current?.(latestErrorTask.error!);
        }, 0);
      }
    }

    setHasCheckedInitialTasks(true);
  }, [isInitialized, hasCheckedInitialTasks, tasks, taskType, processedTaskIds]);

  // Efecto para procesar nuevas tareas en tiempo real
  useEffect(() => {
    if (!isInitialized) return;

    // Buscar tareas completadas del tipo especificado que no hayamos procesado
    const newCompletedTasks = tasks.filter(task =>
      task.type === taskType &&
      task.status === 'completed' &&
      !processedTaskIds.has(task.id) &&
      task.result
    );

    // Buscar tareas con error del tipo especificado que no hayamos procesado
    const newErrorTasks = tasks.filter(task =>
      task.type === taskType &&
      task.status === 'error' &&
      !processedTaskIds.has(task.id) &&
      task.error
    );

    // Procesar nuevas tareas completadas
    if (newCompletedTasks.length > 0) {
      const latestTask = newCompletedTasks[newCompletedTasks.length - 1];

      setProcessedTaskIds(prev => {
        const newSet = new Set(prev);
        newSet.add(latestTask.id);
        return newSet;
      });

      if (onResultRef.current) {
        // Usar setTimeout para evitar actualizaciones durante el render
        setTimeout(() => {
          onResultRef.current?.(latestTask.result);
        }, 0);
      }
    }

    // Procesar nuevas tareas con error
    if (newErrorTasks.length > 0) {
      const latestErrorTask = newErrorTasks[newErrorTasks.length - 1];

      setProcessedTaskIds(prev => {
        const newSet = new Set(prev);
        newSet.add(latestErrorTask.id);
        return newSet;
      });

      if (onErrorRef.current) {
        // Usar setTimeout para evitar actualizaciones durante el render
        setTimeout(() => {
          onErrorRef.current?.(latestErrorTask.error!);
        }, 0);
      }
    }
  }, [isInitialized, tasks, taskType, processedTaskIds]);

  // Función para resetear los IDs procesados (útil para procesar nuevos resultados)
  const resetProcessed = useCallback(() => {
    setProcessedTaskIds(new Set());
    setHasCheckedInitialTasks(false);
    console.log('🔄 Reseteando estado de tareas procesadas');
  }, []);

  // Función para obtener la tarea completada más reciente del tipo especificado
  const getLatestCompletedTask = useCallback(() => {
    const completedTasks = tasks
      .filter(task =>
        task.type === taskType &&
        task.status === 'completed' &&
        task.result
      );
    // Ordenar por fecha de completado (más reciente primero), luego por fecha de creación
    const sorted = completedTasks.sort((a, b) => {
      // Si ambas tienen completedAt, usar esa fecha
      if (a.completedAt && b.completedAt) {
        return b.completedAt.getTime() - a.completedAt.getTime();
      }
      // Si solo una tiene completedAt, esa va primero
      if (a.completedAt && !b.completedAt) return -1;
      if (!a.completedAt && b.completedAt) return 1;
      // Si ninguna tiene completedAt, usar createdAt
      return b.createdAt.getTime() - a.createdAt.getTime();
    });

    const latest = sorted[0];

    return latest;
  }, [tasks, taskType]);

  return {
    resetProcessed,
    getLatestCompletedTask,
    hasCheckedInitialTasks,
    isInitialized
  };
};
