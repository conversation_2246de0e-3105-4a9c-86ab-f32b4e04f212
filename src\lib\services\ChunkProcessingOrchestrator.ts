/**
 * Servicio Orquestador de Procesamiento por Chunks
 * 
 * Este servicio orquesta el procesamiento completo de documentos por chunks,
 * siguiendo los patrones arquitectónicos establecidos en ARCHITECTURE.md.
 * 
 * Responsabilidades:
 * - Detectar si usar chunking basado en tamaño y configuración
 * - Procesar cada chunk individualmente usando los generadores apropiados
 * - Combinar resultados usando ResultCombinerService
 * - Gestionar logging y monitoreo del proceso
 */

import { DocumentChunkingService } from './DocumentChunkingService';
import {
  TestResultCombiner,
  FlashcardResultCombiner,
  SummaryResultCombiner
} from './ResultCombinerService';
import { TokenEstimationService } from './tokenEstimationService';
import {
  ChunkingLimitsValidator,
  type ChunkingLimitsValidationResult
} from './ChunkingLimitsValidator';
import { type Documento } from '@/types/database';
import {
  type DocumentChunkingResult,
  type ChunkCombinationResult,
  type ChunkingContext
} from '@/types/chunking';
import { CHUNKING_LOGGING_CONFIG } from '@/config/chunking';

/**
 * Tipos de procesamiento soportados
 */
export type ProcessingType = 'tests' | 'flashcards' | 'mindmaps' | 'summaries';

/**
 * Configuración para el procesamiento
 */
export interface ProcessingConfig {
  type: ProcessingType;
  cantidad?: number;
  instrucciones?: string;
  enableChunking?: boolean;
  contentType?: 'default' | 'legal' | 'tecnico' | 'temario';
}

/**
 * Resultado del procesamiento orquestado
 */
export interface OrchestrationResult<T = any> {
  result: T;
  wasChunked: boolean;
  processingStats: {
    chunksProcessed: number;
    totalProcessingTime: number;
    tokensEstimated: number;
    tokensActual?: number;
  };
  chunkingDetails?: ChunkCombinationResult<T>;
  limitsValidation?: ChunkingLimitsValidationResult;
}

/**
 * Función de procesamiento para cada tipo
 */
type ProcessorFunction<T> = (
  chunks: import('@/lib/utils/textProcessing').Chunk[],
  config: ProcessingConfig,
  documentos: Documento[]
) => Promise<T>;

/**
 * Servicio Orquestador de Procesamiento por Chunks
 */
export class ChunkProcessingOrchestrator {
  private static instance: ChunkProcessingOrchestrator;

  private constructor() {}

  /**
   * Singleton pattern para garantizar una sola instancia
   */
  public static getInstance(): ChunkProcessingOrchestrator {
    if (!ChunkProcessingOrchestrator.instance) {
      ChunkProcessingOrchestrator.instance = new ChunkProcessingOrchestrator();
    }
    return ChunkProcessingOrchestrator.instance;
  }

  /**
   * Procesa documentos con orquestación automática de chunking
   */
  async processDocuments<T>(
    documentos: Documento[],
    config: ProcessingConfig,
    processor: ProcessorFunction<T>
  ): Promise<OrchestrationResult<T>> {
    const startTime = Date.now();
    const sessionId = this.generateSessionId();

    // Logging inicial
    this.logChunkingEvent('info', 'Iniciando procesamiento de documentos', {
      sessionId,
      documentCount: documentos.length,
      processingType: config.type,
      cantidad: config.cantidad,
      enableChunking: config.enableChunking,
      contentType: config.contentType
    });
    
    try {
      console.log(`🎯 Iniciando procesamiento orquestado: ${config.type}`);

      // Validar entrada
      this.validateInput(documentos, config);

      // Preparar documentos para chunking
      const chunkingStartTime = Date.now();
      const chunkingResult = this.prepareDocumentsForChunking(documentos, config);
      const chunkingTime = Date.now() - chunkingStartTime;

      // Logging de decisión de chunking
      this.logChunkingEvent('info', 'Decisión de chunking tomada', {
        sessionId,
        wasChunked: chunkingResult.wasChunked,
        chunkCount: Array.isArray(chunkingResult.content) ? chunkingResult.content.length : 1,
        chunkingTimeMs: chunkingTime,
        totalContentSize: documentos.reduce((sum, doc) => sum + doc.contenido.length, 0)
      });

      // Validar límites de chunking si se usó chunking
      let limitsValidation: ChunkingLimitsValidationResult | undefined;
      if (chunkingResult.wasChunked) {
        const validator = ChunkingLimitsValidator.getInstance();
        limitsValidation = validator.validateChunkingResult(chunkingResult, config.type);

        // Logging de validación de límites
        this.logChunkingEvent('info', 'Validación de límites completada', {
          sessionId,
          isValid: limitsValidation.isValid,
          violationCount: limitsValidation.violations.length,
          criticalViolations: limitsValidation.violations.filter(v => v.severity === 'critical').length,
          estimations: limitsValidation.estimations
        });

        if (!limitsValidation.isValid) {
          const criticalViolations = limitsValidation.violations.filter(v => v.severity === 'critical');
          if (criticalViolations.length > 0) {
            this.logChunkingEvent('error', 'Procesamiento cancelado por violaciones críticas', {
              sessionId,
              criticalViolations: criticalViolations.map(v => v.description)
            });
            throw new Error(`Procesamiento cancelado por violaciones críticas de límites: ${criticalViolations.map(v => v.description).join(', ')}`);
          }

          this.logChunkingEvent('warn', 'Advertencias de límites detectadas', {
            sessionId,
            warnings: limitsValidation.violations.map(v => v.description)
          });
          console.warn(`⚠️ Advertencias de límites de chunking:`, limitsValidation.violations.map(v => v.description));
        }
      }

      // Estimar tokens antes del procesamiento
      const tokenEstimation = this.estimateTokens(chunkingResult, config);

      // Logging de estimación de tokens
      this.logChunkingEvent('debug', 'Estimación de tokens completada', {
        sessionId,
        estimatedTokens: tokenEstimation,
        processingType: config.type,
        wasChunked: chunkingResult.wasChunked
      });

      let result: T;
      let chunkingDetails: ChunkCombinationResult<T> | undefined;
      const processingStartTime = Date.now();

      if (chunkingResult.wasChunked && Array.isArray(chunkingResult.content)) {
        console.log(`🧩 Procesando con chunking: ${chunkingResult.content.length} chunks`);

        // Pasar objetos Chunk completos en lugar de solo el contenido
        const chunks = chunkingResult.content;

        this.logChunkingEvent('info', 'Iniciando procesamiento con chunking', {
          sessionId,
          chunkCount: chunkingResult.content.length,
          avgChunkSize: chunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / chunks.length
        });

        // Procesar chunks con selección inteligente
        const chunkResult = await processor(chunks, config, documentos);

        this.logChunkingEvent('info', 'Procesamiento con selección inteligente completado', {
          sessionId,
          resultType: typeof chunkResult
        });

        result = chunkResult;

        // Crear detalles de chunking simplificados
        chunkingDetails = {
          combinedResult: chunkResult,
          chunkingContext: {
            totalChunks: chunks.length,
            selectedChunks: chunks.length, // Los generadores ahora manejan la selección
            processingStrategy: 'intelligent_selection',
            combinationMethod: 'internal_selection'
          }
        };
      } else {
        console.log(`📄 Procesando sin chunking`);

        this.logChunkingEvent('info', 'Iniciando procesamiento sin chunking', {
          sessionId,
          contentSize: typeof chunkingResult.content === 'string' ?
            chunkingResult.content.length :
            chunkingResult.content.reduce((sum, chunk) => sum + chunk.content.length, 0)
        });

        // Procesamiento tradicional
        const content = Array.isArray(chunkingResult.content)
          ? chunkingResult.content.map(chunk => chunk.content)
          : [chunkingResult.content];

        result = await processor(content, config, documentos);
      }

      const totalTime = Date.now() - startTime;
      const processingTime = Date.now() - processingStartTime;

      // Logging final con métricas completas
      this.logPerformanceMetrics(sessionId, {
        totalTime,
        chunksProcessed: chunkingResult.wasChunked && Array.isArray(chunkingResult.content)
          ? chunkingResult.content.length
          : 1,
        tokensEstimated: tokenEstimation.totalEstimated,
        chunkingTime,
        processingTime,
        combinationTime: chunkingDetails ?
          (Date.now() - processingStartTime - processingTime) : 0,
        wasChunked: chunkingResult.wasChunked
      });

      // TODO: Logging de comparación de tokens cuando esté disponible actualTokensUsed
      // if (chunkingDetails?.metadata?.actualTokensUsed) {
      //   this.logTokenComparison(
      //     sessionId,
      //     tokenEstimation.totalEstimated,
      //     chunkingDetails.metadata.actualTokensUsed,
      //     config.type
      //   );
      // }

      this.logChunkingEvent('info', 'Procesamiento completado exitosamente', {
        sessionId,
        totalTimeMs: totalTime,
        finalResultType: typeof result
      });

      console.log(`✅ Procesamiento completado en ${totalTime}ms`);

      return {
        result,
        wasChunked: chunkingResult.wasChunked,
        processingStats: {
          chunksProcessed: chunkingResult.wasChunked && Array.isArray(chunkingResult.content)
            ? chunkingResult.content.length
            : 1,
          totalProcessingTime: totalTime,
          tokensEstimated: tokenEstimation.totalEstimated
        },
        chunkingDetails,
        limitsValidation
      };
      
    } catch (error) {
      console.error('❌ Error en procesamiento orquestado:', error);
      throw error;
    }
  }

  /**
   * Valida la entrada del procesamiento
   */
  private validateInput(documentos: Documento[], config: ProcessingConfig): void {
    if (!documentos || documentos.length === 0) {
      throw new Error('No se proporcionaron documentos para procesar');
    }
    
    if (!config.type) {
      throw new Error('Tipo de procesamiento no especificado');
    }
    
    if (['tests', 'flashcards'].includes(config.type) && !config.cantidad) {
      throw new Error(`Cantidad requerida para procesamiento de ${config.type}`);
    }
  }

  /**
   * Prepara documentos para chunking
   */
  private prepareDocumentsForChunking(
    documentos: Documento[],
    config: ProcessingConfig
  ): DocumentChunkingResult {
    // Por ahora, procesar solo el primer documento
    // En el futuro se puede extender para múltiples documentos
    const documento = documentos[0];
    
    return DocumentChunkingService.processDocument(documento, {
      enableChunking: config.enableChunking !== false,
      contentType: config.contentType || 'default'
    });
  }

  /**
   * Estima tokens para el procesamiento
   */
  private estimateTokens(
    chunkingResult: DocumentChunkingResult,
    config: ProcessingConfig
  ): { totalEstimated: number; isWithinLimits: boolean } {
    const contextosString = Array.isArray(chunkingResult.content)
      ? chunkingResult.content.map(chunk => chunk.content)
      : [chunkingResult.content];

    switch (config.type) {
      case 'tests':
        return chunkingResult.wasChunked && Array.isArray(chunkingResult.content)
          ? TokenEstimationService.estimateForTestsWithChunks(chunkingResult.content, config.cantidad || 10)
          : TokenEstimationService.estimateForTests(contextosString, config.cantidad || 10);

      case 'flashcards':
        return chunkingResult.wasChunked && Array.isArray(chunkingResult.content)
          ? TokenEstimationService.estimateForFlashcardsWithChunks(chunkingResult.content, config.cantidad || 10)
          : TokenEstimationService.estimateForFlashcards(contextosString, config.cantidad || 10);

      case 'mindmaps':
        return chunkingResult.wasChunked && Array.isArray(chunkingResult.content)
          ? TokenEstimationService.estimateForMindMapsWithChunks(chunkingResult.content)
          : TokenEstimationService.estimateForMindMaps(contextosString);

      case 'summaries':
        return chunkingResult.wasChunked && Array.isArray(chunkingResult.content)
          ? TokenEstimationService.estimateForSummariesWithChunks(chunkingResult.content)
          : TokenEstimationService.estimateForSummaries(contextosString);
          
      default:
        throw new Error(`Tipo de procesamiento no soportado: ${config.type}`);
    }
  }



  /**
   * Combina resultados usando el combinador apropiado
   */
  private combineResults<T>(
    results: T[],
    config: ProcessingConfig,
    chunkingResult: DocumentChunkingResult,
    documentos: Documento[]
  ): ChunkCombinationResult<T> {
    const totalChunks = results.length;
    const chunkingContexts: ChunkingContext[] = results.map((_, index) => ({
      currentChunk: index + 1,
      totalChunks,
      hasPreviousChunks: index > 0,
      hasNextChunks: index < totalChunks - 1,
      documentSections: Array.isArray(chunkingResult.stats.sectionsDetected) ?
        chunkingResult.stats.sectionsDetected : [],
      chunkingStrategy: chunkingResult.stats.chunkingStrategy
    }));
    
    const sourceDocument = {
      id: documentos[0].id,
      title: documentos[0].titulo,
      totalSize: chunkingResult.sourceDocument.originalSize
    };
    
    switch (config.type) {
      case 'tests':
        return TestResultCombiner.combine(
          results as any[][],
          config.cantidad || 10,
          chunkingContexts,
          sourceDocument
        ) as ChunkCombinationResult<T>;
        
      case 'flashcards':
        return FlashcardResultCombiner.combine(
          results as any[][],
          config.cantidad || 10,
          chunkingContexts,
          sourceDocument
        ) as ChunkCombinationResult<T>;
        
      case 'summaries':
      case 'mindmaps':
        return SummaryResultCombiner.combine(
          results as string[][],
          chunkingContexts,
          sourceDocument
        ) as ChunkCombinationResult<T>;
        
      default:
        throw new Error(`Combinación no soportada para tipo: ${config.type}`);
    }
  }

  /**
   * Genera un ID único para la sesión de procesamiento
   */
  private generateSessionId(): string {
    return `chunk_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Registra eventos de chunking con logging detallado
   */
  private logChunkingEvent(
    level: 'debug' | 'info' | 'warn' | 'error',
    message: string,
    data?: any
  ): void {
    if (CHUNKING_LOGGING_CONFIG.customLogger) {
      CHUNKING_LOGGING_CONFIG.customLogger(level, message, data);
    } else {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [CHUNKING] [${level.toUpperCase()}] ${message}`;

      if (data && CHUNKING_LOGGING_CONFIG.includeStats) {
        console.log(logMessage, data);
      } else {
        console.log(logMessage);
      }
    }
  }

  /**
   * Registra métricas de rendimiento del chunking
   */
  private logPerformanceMetrics(
    sessionId: string,
    metrics: {
      totalTime: number;
      chunksProcessed: number;
      tokensEstimated: number;
      tokensActual?: number;
      chunkingTime?: number;
      processingTime?: number;
      combinationTime?: number;
      wasChunked: boolean;
    }
  ): void {
    this.logChunkingEvent('info', 'Métricas de rendimiento', {
      sessionId,
      ...metrics,
      efficiency: metrics.tokensActual ?
        (metrics.tokensEstimated / metrics.tokensActual).toFixed(2) : 'N/A',
      avgTimePerChunk: metrics.chunksProcessed > 0 ?
        (metrics.processingTime || 0) / metrics.chunksProcessed : 0
    });
  }

  /**
   * Registra comparación entre tokens estimados vs reales
   * TODO: Será usado cuando actualTokensUsed esté disponible en metadata
   */
  /**
   * Método público para logging de comparación de tokens (para uso futuro)
   */
  public logTokenComparison(
    sessionId: string,
    estimated: number,
    actual: number,
    type: string
  ): void {
    const accuracy = ((1 - Math.abs(estimated - actual) / actual) * 100).toFixed(1);
    const difference = actual - estimated;

    this.logChunkingEvent('debug', 'Comparación de tokens', {
      sessionId,
      type,
      estimated,
      actual,
      difference,
      accuracy: `${accuracy}%`,
      status: Math.abs(difference / actual) < 0.1 ? 'PRECISO' : 'DESVIADO'
    });
  }
}

/**
 * Factory para crear instancias del orquestador
 */
export class ChunkProcessingOrchestratorFactory {
  /**
   * Crea una instancia del orquestador
   */
  static create(): ChunkProcessingOrchestrator {
    return ChunkProcessingOrchestrator.getInstance();
  }
}

/**
 * Función de utilidad para procesamiento rápido
 */
export async function processWithChunking<T>(
  documentos: Documento[],
  config: ProcessingConfig,
  processor: ProcessorFunction<T>
): Promise<OrchestrationResult<T>> {
  const orchestrator = ChunkProcessingOrchestratorFactory.create();
  return orchestrator.processDocuments(documentos, config, processor);
}
