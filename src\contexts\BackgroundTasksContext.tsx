'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode, useMemo } from 'react';
import { toast } from 'react-hot-toast';

export interface BackgroundTask {
  id: string;
  type: 'mapa-mental' | 'test' | 'flashcards' | 'plan-estudios' | 'resumen';
  title: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress?: number;
  result?: any;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

interface BackgroundTasksContextType {
  tasks: BackgroundTask[];
  addTask: (task: Omit<BackgroundTask, 'id' | 'status' | 'createdAt'>) => string;
  updateTask: (id: string, updates: Partial<BackgroundTask>) => void;
  removeTask: (id: string) => void;
  getTask: (id: string) => BackgroundTask | undefined;
  getTasksByType: (type: BackgroundTask['type']) => BackgroundTask[];
  clearCompletedTasks: () => void;
  activeTasks: BackgroundTask[];
  completedTasks: BackgroundTask[];
}

const BackgroundTasksContext = createContext<BackgroundTasksContextType | undefined>(undefined);

interface BackgroundTasksProviderProps {
  children: ReactNode;
}

export const BackgroundTasksProvider: React.FC<BackgroundTasksProviderProps> = ({ children }) => {
  const [tasks, setTasks] = useState<BackgroundTask[]>([]);

  const addTask = useCallback((taskData: Omit<BackgroundTask, 'id' | 'status' | 'createdAt'>) => {
    const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newTask: BackgroundTask = {
      ...taskData,
      id,
      status: 'pending',
      createdAt: new Date(),
    };

    setTasks(prev => [...prev, newTask]);

    // Mostrar notificación de inicio
    toast.loading(`Iniciando: ${newTask.title}`, {
      id: `task_start_${id}`,
      duration: 2000,
    });

    return id;
  }, []);

  const updateTask = useCallback((id: string, updates: Partial<BackgroundTask>) => {
    setTasks(prev => prev.map(task => {
      if (task.id === id) {
        const updatedTask = { ...task, ...updates };

        // Manejar notificaciones según el estado
        if (updates.status === 'processing' && task.status === 'pending') {
          toast.dismiss(`task_start_${id}`);
          toast.loading(`Procesando: ${task.title}`, {
            id: `task_processing_${id}`,
          });
        } else if (updates.status === 'completed' && task.status !== 'completed') {
          toast.dismiss(`task_processing_${id}`);
          toast.success(`Completado: ${task.title}`, {
            id: `task_completed_${id}`,
            duration: 4000,
          });
          updatedTask.completedAt = new Date();
        } else if (updates.status === 'error' && task.status !== 'error') {
          toast.dismiss(`task_processing_${id}`);
          toast.error(`Error: ${task.title}`, {
            id: `task_error_${id}`,
            duration: 5000,
          });
        }

        return updatedTask;
      }
      return task;
    }));
  }, []);

  const removeTask = useCallback((id: string) => {
    setTasks(prev => prev.filter(task => task.id !== id));
    // Limpiar notificaciones relacionadas
    toast.dismiss(`task_start_${id}`);
    toast.dismiss(`task_processing_${id}`);
    toast.dismiss(`task_completed_${id}`);
    toast.dismiss(`task_error_${id}`);
  }, []);

  const getTask = useCallback((id: string) => {
    return tasks.find(task => task.id === id);
  }, [tasks]);

  const getTasksByType = useCallback((type: BackgroundTask['type']) => {
    return tasks.filter(task => task.type === type);
  }, [tasks]);

  const clearCompletedTasks = useCallback(() => {
    setTasks(prev => prev.filter(task => task.status !== 'completed' && task.status !== 'error'));
  }, []);

  const activeTasks = useMemo(() => tasks.filter(task =>
    task.status === 'pending' || task.status === 'processing'
  ), [tasks]);

  const completedTasks = useMemo(() => tasks.filter(task =>
    task.status === 'completed' || task.status === 'error'
  ), [tasks]);

  const value: BackgroundTasksContextType = useMemo(() => ({
    tasks,
    addTask,
    updateTask,
    removeTask,
    getTask,
    getTasksByType,
    clearCompletedTasks,
    activeTasks,
    completedTasks,
  }), [tasks, addTask, updateTask, removeTask, getTask, getTasksByType, clearCompletedTasks, activeTasks, completedTasks]);

  return (
    <BackgroundTasksContext.Provider value={value}>
      {children}
    </BackgroundTasksContext.Provider>
  );
};

export const useBackgroundTasks = () => {
  const context = useContext(BackgroundTasksContext);
  if (context === undefined) {
    throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');
  }
  return context;
};
