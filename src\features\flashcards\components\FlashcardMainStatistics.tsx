import React, { useState, useEffect } from 'react';
import { obtenerEstadisticasGenerales } from '@/lib/supabase/estadisticasService';

interface EstadisticasGenerales {
  total: number;
  nuevas: number;
  aprendiendo: number;
  repasando: number;
  aprendidas: number;
  paraHoy: number;
}

interface FlashcardMainStatisticsProps {
  onRefresh?: () => void;
}

const FlashcardMainStatistics: React.FC<FlashcardMainStatisticsProps> = ({ onRefresh }) => {
  const [estadisticas, setEstadisticas] = useState<EstadisticasGenerales | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    cargarEstadisticas();
  }, []);

  const cargarEstadisticas = async () => {
    try {
      setIsLoading(true);
      setError('');
      const stats = await obtenerEstadisticasGenerales();
      setEstadisticas(stats);
    } catch (error) {
      console.error('Error al cargar estadísticas generales:', error);
      setError('Error al cargar estadísticas');
    } finally {
      setIsLoading(false);
    }
  };

  // Función para refrescar estadísticas (llamada desde el componente padre)
  React.useImperativeHandle(onRefresh, () => ({
    refresh: cargarEstadisticas
  }));

  if (isLoading) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-4">Estadísticas</h3>
        <div className="grid grid-cols-5 gap-4">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="text-center">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !estadisticas) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-4">Estadísticas</h3>
        <div className="text-center text-gray-500 py-4">
          {error || 'No se pudieron cargar las estadísticas'}
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold mb-4">Estadísticas</h3>
      <div className="grid grid-cols-5 gap-4">
        {/* Total */}
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {estadisticas.total}
          </div>
          <div className="text-sm text-gray-600">Total</div>
        </div>

        {/* Para hoy */}
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {estadisticas.paraHoy}
          </div>
          <div className="text-sm text-gray-600">Para hoy</div>
        </div>

        {/* Nuevas */}
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {estadisticas.nuevas}
          </div>
          <div className="text-sm text-gray-600">Nuevas</div>
        </div>

        {/* Aprendiendo */}
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {estadisticas.aprendiendo}
          </div>
          <div className="text-sm text-gray-600">Aprendiendo</div>
        </div>

        {/* Aprendidas */}
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {estadisticas.aprendidas}
          </div>
          <div className="text-sm text-gray-600">Aprendidas</div>
        </div>
      </div>
    </div>
  );
};

export default FlashcardMainStatistics;
