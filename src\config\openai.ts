// Configuración centralizada de OpenAI

export const OPENAI_CONFIG = {
  // Modelos específicos por funcionalidad (sin modelo por defecto)
  MODELS: {
    // Para generación de planes de estudio (requiere más precisión)
    PLAN_ESTUDIOS: 'o3-mini-2025-01-31',
    // Para Q&A y conversaciones (puede ser más rápido)
    CONVERSACIONES: 'o3-mini-2025-01-31',

    // Para generación de flashcards (simple)
    FLASHCARDS: 'o3-mini-2025-01-31',

    // Para generación de tests (requiere precisión)
    TESTS: 'o3-mini-2025-01-31',

    // Para mapas mentales (creativo) - Cambia aquí el modelo que quieras usar
    // Modelos disponibles: 'o3-mini', 'o4-mini', 'o1-mini', 'gpt-4o', 'gpt-4o-mini'
    MAPAS_MENTALES: 'o3-2025-04-16', // Modelo de razonamiento con 100k tokens output

    // Para generación de resúmenes (requiere comprensión y síntesis)
    RESUMENES: 'o3-mini-2025-01-31'
  },
  
  // Configuraciones por defecto
  DEFAULTS: {
    temperature: 0.7,
    max_tokens: 4000,
  },
  
  // Configuraciones específicas por tipo de tarea
  TASK_CONFIGS: {
    PLAN_ESTUDIOS: {
      temperature: 0.3, // Más determinístico para planes
      max_tokens: 100000, // Límite muy alto para evitar truncamiento de JSON
    },
    CONVERSACIONES: {
      temperature: 0.7, // Más natural para conversaciones
      max_tokens: 20000,
    },
    FLASHCARDS: {
      temperature: 0.6, // Equilibrado para flashcards
      max_tokens: 100000,
    },
    TESTS: {
      temperature: 0.4, // Muy determinístico para tests
      max_tokens: 100000,
    },
    MAPAS_MENTALES: {
      temperature: 0.8, // Más creativo para mapas (solo para modelos tradicionales)
      max_tokens: 100000, // Aumentado significativamente para mapas complejos
    },
    RESUMENES: {
      temperature: 0.5, // Equilibrado para resúmenes (comprensión + síntesis)
      max_tokens: 100000, // Suficiente para resúmenes detallados
    },
  }
};

// Función helper para obtener configuración por tipo de tarea
export function getOpenAIConfig(taskType: keyof typeof OPENAI_CONFIG.MODELS) {
  return {
    model: OPENAI_CONFIG.MODELS[taskType],
    ...OPENAI_CONFIG.TASK_CONFIGS[taskType],
  };
}

// Función para validar que siempre se use un modelo específico
export function validateModelConfig(taskType: keyof typeof OPENAI_CONFIG.MODELS) {
  const config = getOpenAIConfig(taskType);
  if (!config.model) {
    throw new Error(`No se ha configurado un modelo específico para la tarea: ${taskType}`);
  }
  return config;
}
