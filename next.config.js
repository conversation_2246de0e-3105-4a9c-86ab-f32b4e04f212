/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configuración mínima para evitar problemas

  // Configuración de TypeScript
  typescript: {
    ignoreBuildErrors: false,
  },

  // Configuración de ESLint
  eslint: {
    ignoreDuringBuilds: false,
  },

  // Configuración básica de Webpack
  webpack: (config, { isServer }) => {
    // Solo configuraciones esenciales
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    return config;
  },
};

module.exports = nextConfig;
