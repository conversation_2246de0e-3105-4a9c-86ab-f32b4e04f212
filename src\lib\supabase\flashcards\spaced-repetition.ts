/**
 * Algoritmo de Repetición Espaciada (SM-2 Modificado)
 * 
 * Responsabilidades:
 * - Implementación del algoritmo SM-2
 * - Registro de respuestas y actualización de progreso
 * - Gestión del historial de revisiones
 * - Cálculo de intervalos y factores de facilidad
 * 
 * ALGORITMO IMPLEMENTADO (SM-2 Modificado):
 * ========================================
 * 
 * 1. FACTOR DE FACILIDAD (EF):
 *    - Inicial: 2.5
 *    - Rango: 1.3 - 2.5
 *    - Ajustes por dificultad:
 *      * Difícil: EF = max(1.3, EF - 0.3)
 *      * Normal: EF = EF - 0.15
 *      * Fácil: EF = EF + 0.1
 * 
 * 2. INTERVALOS DE REPETICIÓN:
 *    - Primera repetición: 1 día
 *    - Segunda repetición: 6 días
 *    - Siguientes: intervalo_anterior * factor_facilidad
 * 
 * 3. ESTADOS DE FLASHCARD:
 *    - nuevo: Sin estudiar
 *    - aprendiendo: 1-2 repeticiones
 *    - repasando: 3+ repeticiones, intervalo ≤ 30 días
 *    - aprendido: intervalo > 30 días
 * 
 * 4. REINICIO POR DIFICULTAD:
 *    - Si se marca como "difícil", se reinicia a repeticiones = 0, intervalo = 1
 */

import {
  supabase,
  DificultadRespuesta,
  RevisionHistorial
} from '../supabaseClient';

/**
 * Registra una respuesta a una flashcard y actualiza su progreso
 * Versión mejorada para evitar errores 409
 */
export async function registrarRespuestaFlashcard(
  flashcardId: string,
  dificultad: DificultadRespuesta
): Promise<boolean> {
  try {
    // Primero intentamos obtener el progreso existente
    let factorFacilidad = 2.5;
    let intervalo = 1;
    let repeticiones = 0;
    let estado: 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido' = 'nuevo';
    let progresoExiste = false;

    // Intentar obtener progreso existente
    const { data: progresoExistente, error: errorConsulta } = await supabase
      .from('progreso_flashcards')
      .select('factor_facilidad, intervalo, repeticiones, estado')
      .eq('flashcard_id', flashcardId)
      .single();

    if (!errorConsulta && progresoExistente) {
      factorFacilidad = progresoExistente.factor_facilidad || 2.5;
      intervalo = progresoExistente.intervalo || 1;
      repeticiones = progresoExistente.repeticiones || 0;
      estado = progresoExistente.estado || 'nuevo';
      progresoExiste = true;
    }

    // Aplicar el algoritmo SM-2 para calcular el nuevo progreso
    let nuevoFactorFacilidad = factorFacilidad;
    let nuevoIntervalo = intervalo;
    let nuevasRepeticiones = repeticiones;
    let nuevoEstado = estado;

    // Ajustar el factor de facilidad según la dificultad reportada
    if (dificultad === 'dificil') {
      nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);
      nuevasRepeticiones = 0;
      nuevoIntervalo = 1;
      nuevoEstado = 'aprendiendo';
    } else {
      nuevasRepeticiones++;

      if (dificultad === 'normal') {
        nuevoFactorFacilidad = factorFacilidad - 0.15;
      } else if (dificultad === 'facil') {
        nuevoFactorFacilidad = factorFacilidad + 0.1;
      }

      nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));

      // Calcular el nuevo intervalo
      if (nuevasRepeticiones === 1) {
        nuevoIntervalo = 1;
        nuevoEstado = 'aprendiendo';
      } else if (nuevasRepeticiones === 2) {
        nuevoIntervalo = 6;
        nuevoEstado = 'repasando';
      } else {
        nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);
        nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';
      }
    }

    // Calcular la próxima fecha de revisión
    const ahora = new Date();
    const proximaRevision = new Date(ahora);
    proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);

    // Guardar el nuevo progreso usando insert o update según corresponda
    let errorProgreso = null;

    if (progresoExiste) {
      // Actualizar progreso existente
      const { error } = await supabase
        .from('progreso_flashcards')
        .update({
          factor_facilidad: nuevoFactorFacilidad,
          intervalo: nuevoIntervalo,
          repeticiones: nuevasRepeticiones,
          estado: nuevoEstado,
          ultima_revision: ahora.toISOString(),
          proxima_revision: proximaRevision.toISOString(),
        })
        .eq('flashcard_id', flashcardId);

      errorProgreso = error;
    } else {
      // Crear nuevo progreso
      const { error } = await supabase
        .from('progreso_flashcards')
        .insert({
          flashcard_id: flashcardId,
          factor_facilidad: nuevoFactorFacilidad,
          intervalo: nuevoIntervalo,
          repeticiones: nuevasRepeticiones,
          estado: nuevoEstado,
          ultima_revision: ahora.toISOString(),
          proxima_revision: proximaRevision.toISOString(),
        });

      errorProgreso = error;
    }

    if (errorProgreso) {
      console.error('Error al guardar progreso:', errorProgreso);
      return false;
    }

    // Guardar en el historial de revisiones
    const { error: errorHistorial } = await supabase
      .from('historial_revisiones')
      .insert({
        flashcard_id: flashcardId,
        dificultad,
        factor_facilidad: nuevoFactorFacilidad,
        intervalo: nuevoIntervalo,
        repeticiones: nuevasRepeticiones,
        fecha: ahora.toISOString(),
      });

    if (errorHistorial) {
      // No retornamos false aquí porque el progreso ya se guardó correctamente
      console.error('Error al guardar historial:', errorHistorial);
    }

    return true;
  } catch (error) {
    console.error('Error en registrarRespuestaFlashcard:', error);
    return false;
  }
}

// Alias para mantener compatibilidad con el código existente
export const actualizarProgresoFlashcard = registrarRespuestaFlashcard;

/**
 * Guarda una revisión en el historial
 */
export async function guardarRevisionHistorial(
  flashcardId: string,
  dificultad: DificultadRespuesta,
  factorFacilidad: number,
  intervalo: number,
  repeticiones: number
): Promise<string | null> {
  const { data, error } = await supabase
    .from('historial_revisiones')
    .insert([{
      flashcard_id: flashcardId,
      dificultad,
      factor_facilidad: factorFacilidad,
      intervalo,
      repeticiones
    }])
    .select();

  if (error) {
    console.error('Error al guardar revisión en historial:', error);
    return null;
  }

  return data?.[0]?.id || null;
}
